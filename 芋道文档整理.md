# 芋道文档整理

## 项目简介

芋道是一个基于 Spring Boot + MyBatis Plus + Vue & Element 的后台管理系统，旨在为开发者提供一个易于上手的开发平台。该项目集成了众多主流技术栈，提供了完整的权限管理、代码生成、多租户、AI大模型、工作流等功能。

![架构图](/img/common/ruoyi-vue-pro-architecture.png)

## 技术栈详细说明

### 系统环境依赖
- **JDK**: Java Development Kit (JDK 17 或 JDK 8)
- **Maven**: Java 管理和构建工具 (>= 3.5.4)
- **Nginx**: 高性能 Web 服务器

### 主框架组件
- **Spring Boot**: 应用开发框架 (3.3.1)
- **Spring MVC**: MVC 框架 (6.1.10)
- **Spring Security**: Spring 安全框架 (6.3.1)
- **Hibernate Validator**: 参数校验组件 (8.0.1)

### 存储层组件
- **MySQL**: 数据库服务器 (>= 5.7)
- **Druid**: JDBC 连接池、监控组件 (1.2.23)
- **MyBatis Plus**: MyBatis 增强工具包 (3.5.7)
- **Dynamic Datasource**: 动态数据源 (4.3.1)
- **Redis**: 键值数据库 (>= 5.0)
- **Redisson**: Redis 客户端 (3.32.0)

### 中间件组件
- **Flowable**: 工作流引擎 (7.0.0)
- **Quartz**: 任务调度组件 (2.3.2)

### 系统监控工具
- **Spring Boot Admin**: Spring Boot 监控平台 (3.3.2)
- **SkyWalking**: 分布式应用追踪系统 (9.0.0)

### 单元测试框架
- **JUnit**: Java 单元测试框架 (5.10.1)
- **Mockito**: Java Mock 框架 (5.7.0)

### 技术栈特点

- **Java 后端**：`master` 分支为 JDK 8 + Spring Boot 2.7，`master-jdk17` 分支为 JDK 17/21 + Spring Boot 3.2
- **管理后台的电脑端**：Vue3 提供 element-plus、vben(ant-design-vue) 两个版本，Vue2 提供 element-ui 版本
- **管理后台的移动端**：采用 uni-app 方案，一份代码多终端适配，同时支持 APP、小程序、H5！
- **后端采用**：Spring Boot、MySQL + MyBatis Plus、Redis + Redisson
- **数据库支持**：MySQL、Oracle、PostgreSQL、SQL Server、MariaDB、国产达梦 DM、TiDB 等
- **消息队列支持**：Event、Redis、RabbitMQ、Kafka、RocketMQ 等
- **权限认证**：Spring Security & Token & Redis，支持多终端、多种用户的认证系统，支持 SSO 单点登录
- **权限控制**：支持加载动态权限菜单，按钮级别权限控制，Redis 缓存提升性能
- **多租户系统**：支持 SaaS 多租户系统，可自定义每个租户的权限，提供透明化的多租户底层封装
- **工作流**：使用 Flowable，支持动态表单、在线设计流程、会签 / 或签、多种任务分配方式
- **代码生成器**：高效率开发，使用代码生成器可以一键生成 Java、Vue 前后端代码、SQL 脚本、接口文档，支持单表、树表、主子表
- **实时通信**：采用 Spring WebSocket 实现，内置 Token 身份校验，支持 WebSocket 集群
- **第三方集成**：集成微信小程序、微信公众号、企业微信、钉钉等三方登陆，集成支付宝、微信等支付与退款
- **云服务集成**：集成阿里云、腾讯云等短信渠道，集成 MinIO、阿里云、腾讯云、七牛云等云存储服务
- **报表设计**：集成报表设计器、大屏设计器，通过拖拽即可生成酷炫的报表与大屏

## 项目结构

### 后端结构

#### 1. yudao-dependencies
该模块是一个 Maven Bom，只有一个 pom.xml 文件，定义项目中所有 Maven 依赖的**版本号**，解决依赖冲突问题。

### 前端结构

#### 1. yudao-ui-admin-vben
目录使用 Monorepo 管理，项目结构如下

#### 2. yudao-admin-ui-uniapp
TODO 待补充

## Excel 导入导出功能

### 1. Excel 导出

以 [系统管理 -> 岗位管理] 菜单为例子，讲解它 Excel 导出的实现。

#### 1.1 后端导出实现

在 PostController 类中，定义 `/admin-api/system/post/export` 导出接口。代码如下：

```java
@GetMapping("/export")
@Operation(summary = "岗位管理")
@PreAuthorize("@ss.hasPermission('system:post:export')")
@ApiAccessLog(operateType = EXPORT)
public void export(HttpServletResponse response, @Validated PostPageReqVO reqVO) throws IOException {
     // ① 查询数据
    reqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
    List<PostDO> list = postService.getPostPage(reqVO).getList();
    // ② 导出 Excel
    ExcelUtils.write(response, "岗位数据.xls", "岗位列表", PostRespVO.class,
            BeanUtils.toBean(list, PostRespVO.class));
}
```

**实现步骤：**
1. 将从数据库中查询出来的列表，一般可以复用分页接口，需要设置 `.setPageSize(PageParam.PAGE_SIZE_NONE)` 不过滤分页。
2. 将 PostDO 列表，转换成 PostRespVO 列表，之后通过 ExcelUtils 转换成 Excel 文件，返回给前端。

#### 1.1.2 ExcelUtils 写入

ExcelUtils 的 `#write(...)` 方法，将列表以 Excel 响应给前端。

#### 1.2 前端导出实现

在 post/index.vue 界面，定义 `#handleExport()` 操作。

### 2. Excel 导入

#### 2.1 后端导入实现

##### 2.1.2 ExcelUtils 读取

ExcelUtils 的 `#read(...)` 方法，读取 Excel 文件成列表。

## 实战案例

### 0.2 树形列表

可参考 [系统管理 -> 部门管理] 菜单：

- **API 接口**：/src/api/system/dept/index.ts
- **列表界面**：/src/views/system/dept/index.vue
- **表单界面**：/src/views/system/dept/DeptForm.vue

## 工具类 Util

### Lombok

Lombok 是一个 Java 工具，通过使用其定义的注解，自动生成常见的冗余代码，提升开发效率。

在项目的根目录有 lombok.config 全局配置文件，开启链式调用、生成的 toString/hashcode/equals 方法需要调用父方法。

## 数据库文档

`yudao-module-infra` 的 DatabaseDocController 类，基于 Screw 工具，生成数据库表结构的文档。

访问 [基础设施 -> 数据库文档] 菜单，可以查看项目的数据库文档。

**注意**：最新版本的代码，已经移除"数据库文档"功能。原因是，该功能比较小众，可能只有极少数的用户需要。如果你系统里需要，可以参考相关提交把代码复制、粘贴回来。

## 表结构变更（版本升级）

### 删除不需要的字典数据

```sql
# 删除 `yudao-module-mp` 不要的字典数据
DELETE FROM system_dict_type WHERE type LIKE 'mp_%';
DELETE FROM system_dict_data WHERE dict_type LIKE 'mp_%';

# 删除 `yudao-module-report` 不要的字典数据
DELETE FROM system_dict_type WHERE type LIKE 'report_%';
DELETE FROM system_dict_data WHERE dict_type LIKE 'report_%';
```

## 数据库转换工具

### 安装依赖

```bash
pip3 install simple-ddl-parser
```

### 生成其他数据库脚本

在 `sql/tools/` 目录下，执行如下命令打印生成 postgres 的脚本内容，其他可选参数有：`oracle`、`sqlserver`、`dm8`、`kingbase`、`opengauss`：

```bash
python3 convertor.py postgres
```

## 快速启动

### 后端项目启动

需要安装和配置相关环境，包括 Java、Maven、数据库等。

### 前端项目启动

#### uni-app 商城移动端

1. 克隆 yudao-mall-uniapp 项目，并 Star 关注下该项目
2. 下载 HBuilder 工具，并进行安装
3. 点击 HBuilder 的 [文件 -> 导入 -> 从本地项目导入...] 菜单，选择克隆的 `yudao-mall-uniapp` 目录
4. 执行如下命令，安装 npm 依赖：

```bash
# 安装 npm 依赖
npm i
```

## 模块迁移指南

### 后端代码迁移

#### 场景：我要做 REPORT 大屏报表

将 `yudao-module-report` 模块，从【完整版】复制到【精简版】。然后参考《大屏手册》文档，测试迁移的正确性。

## AI 大模型功能

芋道项目集成了丰富的 AI 大模型功能，支持多种主流模型接入：

### AI 功能模块
- **AI 聊天对话**: 智能对话系统
- **AI 绘画创作**: 图像生成功能
- **AI 知识库**: 知识管理和检索
- **AI 音乐创作**: 音乐生成功能
- **AI 写作助手**: 文本创作辅助
- **AI 思维导图**: 思维导图生成
- **AI 工具调用**: Function Calling 功能
- **AI 工作流**: 工作流编排
- **Dify 工作流**: Dify 平台集成
- **FastGPT 工作流**: FastGPT 平台集成
- **Coze 智能体**: Coze 平台集成

### 支持的模型平台
- **OpenAI**: GPT 系列模型
- **通义千问**: 阿里云大模型
- **DeepSeek**: DeepSeek 模型
- **字节豆包**: 字节跳动模型
- **腾讯混元**: 腾讯云模型
- **硅基流动**: 硅基流动平台
- **MiniMax**: MiniMax 模型
- **月之暗灭**: Moonshot 模型
- **百川智能**: 百川模型
- **文心一言**: 百度模型
- **LLAMA**: Meta 开源模型
- **智谱 GLM**: 智谱 AI 模型
- **讯飞星火**: 科大讯飞模型
- **微软 OpenAI**: Azure OpenAI
- **谷歌 Gemini**: Google 模型
- **Stable Diffusion**: 图像生成模型
- **Midjourney**: 图像生成平台
- **Suno**: 音乐生成平台

### 通义千问模型接入示例

```java
@Resource
private TongYiChatModel chatModel;

@Resource
private TongYiImageModel imageModel;
```

配置示例：
```yaml
spring:
  ai:
    dashscope: # 通义千问
      api-key: "你的密钥"
      custom-api-url: "你的自定义 API URL"
```

## 公众号系统功能

芋道项目提供了完整的微信公众号管理系统：

### 功能模块
- **公众号接入**: 账号配置和接入
- **公众号粉丝**: 粉丝管理和分析
- **公众号标签**: 标签管理和分组
- **公众号消息**: 消息收发管理
- **自动回复**: 智能回复设置
- **公众号菜单**: 自定义菜单配置
- **公众号素材**: 素材库管理
- **公众号图文**: 图文消息编辑发布
- **公众号统计**: 数据统计分析

### 消息管理实现

前端实现：
```javascript
// 前端路径: /@views/mp/message
```

后端控制器：
```java
// 后端控制器: MpMessageController
```

## 运维部署指南

### Docker 部署

芋道项目支持 Docker 容器化部署：

```bash
# 构建镜像
docker build -t yudao-server .

# 运行容器
docker run -d -p 8080:8080 yudao-server
```

### Kubernetes 部署

支持 K8s 集群部署，提供完整的 YAML 配置文件：

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: yudao-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: yudao-server
  template:
    metadata:
      labels:
        app: yudao-server
    spec:
      containers:
      - name: yudao-server
        image: yudao-server:latest
        ports:
        - containerPort: 8080
```

### 监控告警

集成多种监控工具：
- **Prometheus**: 指标收集
- **Grafana**: 可视化监控
- **SkyWalking**: 链路追踪
- **Spring Boot Admin**: 应用监控

### 性能优化

- **数据库优化**: 索引优化、SQL 调优
- **缓存策略**: Redis 缓存、本地缓存
- **JVM 调优**: 内存配置、GC 优化
- **负载均衡**: Nginx 配置、集群部署

## 系统配置与安全

### 系统配置功能
- **短信配置**: 支持多种短信服务商
- **邮件配置**: SMTP 邮件服务配置
- **站内信配置**: 系统内部消息通知
- **数据脱敏、字段权限**: 敏感数据保护
- **敏感词过滤**: 内容安全管控
- **地区 & IP 库**: 地理位置和 IP 管理

### 验证码配置

后端禁用验证码：
```yaml
# application-local.yaml
yudao:
  captcha:
    enable: false
```

前端禁用验证码：
```bash
# Vue2.X 项目 .env 文件
VUE_APP_DOC_ENABLE=false

# Vue3.X 项目 .env 文件
VITE_APP_CAPTCHA_ENABLE=false
```

### 文件存储配置

支持多种文件存储方式，以七牛云 S3 为例：

```yaml
# 文件配置 - S3 对象存储器 (七牛云示例)
节点地址: s3.cn-south-1.qiniucs.com
存储 bucket: ruoyi-vue-pro
accessKey: 3TvrJ70gl2Gt6IBe7_IZT1F6i_k0iMuRtyEv4EyS
accessSecret: wd0tbVBYlp0S-ihA8Qg2hPLncoP83wyrIq24OZuY
自定义域名: http://test.yudao.iocoder.cn
是否 Path Style: false
```

## 数据库支持

### 达梦数据库支持

芋道项目支持国产达梦数据库，配置示例：

#### Docker 启动达梦数据库
```bash
docker load -i dm8_20240715_x86_rh6_rq_single.tar

docker run -d -p 5236:5236 \
    --restart=unless-stopped \
    --name dm8_test \
    --privileged=true \
    -e PAGE_SIZE=16 \
    -e LD_LIBRARY_PATH=/opt/dmdbms/binn \
    -e EXTENT_SIZE=32 \
    -e BLANK_PAD_MODE=1 \
    -e LOG_SIZE=1024 \
    -e UNICODE_FLAG=1 \
    -e LENGTH_IN_CHAR=1 \
    -e INSTANCE_NAME=dm8_test \
    -v $PWD/dm8_test:/opt/dmdbms/data \
    dm8_single:dm8_20240715_rev232765_x86_rh6_64
```

#### 项目配置

修改 `pom.xml`：
```xml
<dependency>
    <groupId>com.dm</groupId>
    <artifactId>dmjdbc</artifactId>
    <version>**********</version>
    <!-- 移除 optional 属性 -->
</dependency>
```

修改 `application-local.yaml`：
```yaml
spring:
  datasource:
    url: jdbc:dm://localhost:5236/dm8
    username: SYSDBA
    password: PWD
    driver-class-name: dm.jdbc.driver.DmDriver
```

## 运维与部署

### 宝塔面板部署

#### 安装宝塔面板 (CentOS 9)
```bash
url=https://download.bt.cn/install/install_lts.sh;if [ -f /usr/bin/curl ];then curl -sSO $url;else wget -O install_lts.sh $url;fi;bash install_lts.sh ed8484bec
```

#### 编译后端项目
```bash
mvn clean package -Dmaven.test.skip=true
```

### Go-View 大屏项目

启动 Go-View 前端项目：
```bash
# 配置 npm 镜像源
npm config set registry https://registry.npmmirror.com

# 全局安装 pnpm
npm install -g pnpm

# 安装项目依赖
pnpm install

# 启动开发服务器
npm run dev
```

### 单元测试配置

在 `src/test/resources` 目录下创建 `application-unit-test.yaml`：
```yaml
# 示例配置
spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driverClassName: org.h2.Driver
    username: sa
    password: 
redis:
  host: 127.0.0.1
  port: 16379
```

## 多租户功能移除

如需移除多租户功能，可按以下步骤操作：

### 删除后端多租户相关包
```java
// 删除以下包
cn.iocoder.yudao.module.system.api.tenant
cn.iocoder.yudao.module.system.controller.admin.tenant
cn.iocoder.yudao.module.system.service.tenant
cn.iocoder.yudao.module.system.test.service.tenant
cn.iocoder.yudao.module.system.dal.dataobject.tenant
cn.iocoder.yudao.module.system.dal.mysql.tenant
cn.iocoder.yudao.module.system.convert.tenant
```

### 移除依赖
```xml
<dependency>
    <groupId>cn.iocoder.yudao</groupId>
    <artifactId>yudao-spring-boot-starter-biz-tenant</artifactId>
</dependency>
```

### 修改基类继承
```java
// 将继承 TenantBaseDO 改为继承 BaseDO
public class TenantBaseDO extends BaseDO {
    // ...
}
```

## 更新日志

项目版本更新记录：
- **v2-6-2**: 开发中
- **v2-6-1**: 2025-07-19 发布
- **v2-6-0**: 2025-06-07 发布
- **v2.5.0**: 2025-05-13 发布
- **v2.4.2**: 2025-04-12 发布
- **v2.4.1**: 2025-02-09 发布
- **v2.4.0**: 2024-12-31 发布
- **v2.3.0**: 2024-10-07 发布
- **v2.2.0**: 2024-08-02 发布
- **v2.1.0**: 2024-05-05 发布
- **v2.0.1**: 2024-03-01 发布
- **v2.0.0**: 2024-01-26 发布

## 学习资源

### 源码解析

#### 微服务篇

- Dubbo 72 讲
- Spring Cloud Gateway 25 讲
- 注册中心 Eureka 23 讲
- 配置中心 Apollo 34 讲
- 服务保障 Hystrix 9 讲

**建议**：建议阅读 Dubbo 源码解析，它是微服务架构的通信基石，可能是最值得死磕学习的。Dubbo 这种框架，是足够升多个 P8 工程师的。

### 官方文档
- [芋道官网](https://www.iocoder.cn/)
- [在线演示](http://dashboard.yudao.iocoder.cn/)
- [开发文档](https://doc.iocoder.cn/)

### 视频教程
- [B站视频教程](https://space.bilibili.com/389359187)
- [芋道源码解析](https://www.iocoder.cn/categories/Spring-Boot/)

### 技术交流
- QQ群：17375211
- 微信群：添加微信号 YunaiV 邀请入群

## 项目地址

### 后端项目
- [GitHub](https://github.com/YunaiV/ruoyi-vue-pro)
- [Gitee](https://gitee.com/zhijiantianya/ruoyi-vue-pro)

### 前端项目
- [Vue3 + Element Plus](https://github.com/yudaocode/yudao-ui-admin-vue3)
- [Vue2 + Element UI](https://github.com/YunaiV/ruoyi-vue-pro/tree/master/yudao-ui-admin)
- [React + Ant Design](https://github.com/YunaiV/ruoyi-vue-pro/tree/master/yudao-ui-admin-react)
- [微信小程序](https://github.com/YunaiV/ruoyi-vue-pro/tree/master/yudao-ui-app)

## 版权信息

😆 为开源继绝学，我辈义不容辞！

2017 年，艿艿创建「芋道源码」公众号，帮助了 20w+ 工程师学习优秀框架的源码。

2019 年，看了 Gitee 和 Github 非常多的业务开源项目，无法到达代码整洁、架构整洁。

于是，艿艿利用休息时间，每天肝到晚上 1 点多，如此便有了芋道管理后台 + 微信小程序。

Copyright © 2019-2025芋道源码 | MIT License