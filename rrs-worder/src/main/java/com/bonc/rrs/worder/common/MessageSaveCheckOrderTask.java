package com.bonc.rrs.worder.common;

import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.rrs.byd.domain.PushCheckOrder;
import com.bonc.rrs.byd.domain.PushCheckOrderPicAttachment;
import com.bonc.rrs.byd.domain.PushFile;
import com.bonc.rrs.byd.domain.TempLink;
import com.bonc.rrs.byd.enums.ConstantPool;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.service.IBydApiService;
import com.bonc.rrs.serviceprovider.po.RegionCode;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.util.FileUtils;
import com.bonc.rrs.util.SmsUtil;
import com.bonc.rrs.worder.dao.WorderIntfMessageDao;
import com.bonc.rrs.worder.entity.*;
import com.bonc.rrs.worder.entity.dto.WorderTemplateDto;
import com.bonc.rrs.worder.service.BizRegionService;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.worder.service.WorderTemplateService;
import com.bonc.rrs.workManager.service.SysFilesService;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.renrenwithactiviti.modules.job.task.ITask;
import com.youngking.renrenwithactiviti.modules.sys.entity.BrandEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.BrandService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.util.ThreadContext;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * {@code @description} 检查工单入库定时任务
 * {@code @date} 2025/07/21 17:00
 */
@Slf4j
@Component("messageSaveCheckOrderTask")
public class MessageSaveCheckOrderTask implements ITask {

    // 业务配置常量
    private static final int CHECK_ORDER_TYPE = 8;

    // 系统配置常量
    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
    private static final String EMOJI_REGEX = "([\\u20A0-\\u32FF\\uD83C-\\uDFFF\\u2600-\\u27FF])|([\\uD830-\\uD83F][\\uDC00-\\uDFFF])";

    // 网络配置常量
    private static final int CONNECTION_TIMEOUT = (int) TimeUnit.SECONDS.toMillis(10);
    private static final int READ_TIMEOUT = (int) TimeUnit.SECONDS.toMillis(30);
    private static final String DEFAULT_IMAGE_EXTENSION = ".jpg";
    private static final String USER_AGENT = "Mozilla/5.0 (compatible; RRS-Worder/1.0)";

    @Autowired
    private WorderIntfMessageDao worderIntfMessageDao;
    @Autowired
    private BrandService brandService;
    @Autowired
    private BizRegionService bizRegionService;
    @Autowired
    private WorderTemplateService worderTemplateService;
    @Autowired
    private WorderInformationService worderInformationService;
    @Autowired
    private SysFilesService sysFilesService;

    private IBydApiService iBydApiService;

    private void createDefaultLoginUser() {
        DefaultWebSecurityManager defaultWebSecurityManager = new DefaultWebSecurityManager();
        SecurityUtils.setSecurityManager(defaultWebSecurityManager);

        // 由于跳过登陆认证，需要设置固定的登陆信息
        SysUserEntity sysUserEntity = new SysUserEntity();
        sysUserEntity.setUserId(ConstantPool.NEWS_OPERATOR);
        sysUserEntity.setUsername(ConstantPool.NEWS_OPERATOR_NAME);
        // 创建一个Subject.Builder
        Subject.Builder builder = new Subject.Builder();
        // 设置身份信息
        PrincipalCollection principals = new SimplePrincipalCollection(sysUserEntity, ConstantPool.NEWS_OPERATOR_NAME);
        builder.principals(principals);
        // 设置是否已经认证
        builder.authenticated(true);
        // 创建Subject实例
        Subject subject = builder.buildSubject();
        ThreadContext.bind(subject);
    }

    @Override
    @Lock4j(expire = 330000)
    public void run(String params) {
        log.info("检查工单入库定时任务 ---- 开始");
        // 查询检查工单消息
        List<WorderIntfMessageEntity> worderIntfMessageEntityList = worderIntfMessageDao.queryNotSaveOrder(CHECK_ORDER_TYPE);

        if (worderIntfMessageEntityList != null) {
            createDefaultLoginUser();
            for (WorderIntfMessageEntity worderIntfMessageEntity : worderIntfMessageEntityList) {
                log.info("message save check order {} start.", worderIntfMessageEntity.getOrderCode());
                try {
                    PushCheckOrder pushCheckOrder = JSON.parseObject(worderIntfMessageEntity.getData(), PushCheckOrder.class);
                    saveCheckOrder(worderIntfMessageEntity.getId(), pushCheckOrder);
                } catch (Exception e) {
                    log.error("处理检查工单失败, messageId: {}, orderCode: {}",
                        worderIntfMessageEntity.getId(), worderIntfMessageEntity.getOrderCode(), e);
                    updateMessageTypeFail(worderIntfMessageEntity.getId(),
                        "处理失败: " + (e.getMessage() != null ? e.getMessage() : e.getClass().getSimpleName()), worderIntfMessageEntity.getOrderCode());
                }

                log.info("message save check order {} end", worderIntfMessageEntity.getOrderCode());
            }
        }
        log.info("检查工单入库定时任务 ---- 结束");
    }

    public void updateMessageTypeFail(Integer messageId, String errorMsg, String orderCode) {
        worderIntfMessageDao.updateMessageTypeById(messageId, 2, errorMsg);
        SmsUtil.sendSms("15910305046", "推送比亚迪检查订单入库失败,订单号:" + orderCode, "【到每家科技服务】");
    }

    private void saveCheckOrder(Integer messageId, PushCheckOrder pushCheckOrder) {
        try {
            // 验证品牌
            BrandEntity brandEntity = validateAndGetBrand(messageId, pushCheckOrder);
            if (brandEntity == null) {
                return;
            }

            // 验证地区
            RegionCode regionCode = validateAndGetRegion(messageId, pushCheckOrder);
            if (regionCode == null) {
                return;
            }

            // 验证模板
            WorderTemplateDto worderTemplateDto = validateAndGetTemplate(messageId, pushCheckOrder, brandEntity,regionCode);
            if (worderTemplateDto == null) {
                return;
            }

            // 验证订单号是否重复
            if (isOrderDuplicate(messageId, pushCheckOrder, worderTemplateDto)) {
                return;
            }

            // 创建工单信息
            WorderInfoEntity worderInfoEntity = createWorderInfoEntity(pushCheckOrder, brandEntity, regionCode, worderTemplateDto);

            // 创建扩展字段
            List<WorderExtFieldEntity> worderExtFieldEntityList = createExtendedFields(pushCheckOrder, worderInfoEntity);
            worderInfoEntity.setWorderExtFieldList(worderExtFieldEntityList);

            R r = saveWorderInformation(worderInfoEntity);

            if (!IntegerEnum.ZERO.getValue().equals(r.get("code"))) {
                log.info("保存检查工单失败, orderCode: {}, msg: {}", pushCheckOrder.getSubOrderCode(), r.get("msg"));
                updateMessageTypeFail(messageId, r.get("msg") + "", pushCheckOrder.getSubOrderCode());
                return;
            }

            String worderNo = r.get("worderNo") + "";
            processSuccessfulOrder(messageId, worderNo);
        } catch (Exception e) {
            log.error("保存检查工单异常, messageId: {}, orderCode: {}", messageId, pushCheckOrder.getSubOrderCode(), e);
            String errorMsg = "保存工单失败: " + (e.getMessage() != null ? e.getMessage() : e.getClass().getSimpleName());
            updateMessageTypeFail(messageId, errorMsg, pushCheckOrder.getSubOrderCode());
        }
    }

    /**
     * 处理成功的订单
     */
    private void processSuccessfulOrder(Integer messageId, String worderNo) {
        WorderInformationEntity worderInformationEntity = getWorderInformationByWorderNo(worderNo);
        if (worderInformationEntity != null) {
            worderIntfMessageDao.updateWorderIdById(messageId, worderInformationEntity.getWorderId());
            // 自动派单
            goAutoSendWorder(worderNo);
        } else {
            log.warn("未找到工单信息, worderNo: {}", worderNo);
        }
    }

    /**
     * 验证并获取品牌信息
     */
    private BrandEntity validateAndGetBrand(Integer messageId, PushCheckOrder pushCheckOrder) {
        BrandEntity brandEntity = brandService.queryBrandByCompanyBrandId(pushCheckOrder.getCarBrand());
        if (brandEntity == null) {
            log.info("message save check order {} 非法品牌", pushCheckOrder.getSubOrderCode());
            updateMessageTypeFail(messageId, "非法品牌", pushCheckOrder.getSubOrderCode());
        }
        return brandEntity;
    }

    /**
     * 验证并获取地区信息
     */
    private RegionCode validateAndGetRegion(Integer messageId, PushCheckOrder pushCheckOrder) {
        RegionCode regionCode = convertRegion(pushCheckOrder.getProvinceCode(), pushCheckOrder.getCityCode(), pushCheckOrder.getAreaCode());
        if (regionCode.getProvinceCode() == null || regionCode.getCityCode() == null || regionCode.getAreaCode() == null) {
            log.info("message save check order {} 地区不匹配", pushCheckOrder.getSubOrderCode());
            updateMessageTypeFail(messageId, "地区不匹配", pushCheckOrder.getSubOrderCode());
            return null;
        }
        return regionCode;
    }

    /**
     * 验证并获取模板信息
     */
    private WorderTemplateDto validateAndGetTemplate(Integer messageId, PushCheckOrder pushCheckOrder, BrandEntity brandEntity, RegionCode regionCode) {
        List<WorderTemplateDto> worderTemplateDtoList = worderTemplateService.findTemplateInfoByBrandIdAndWorderTypeIdAndRegion(
                brandEntity.getId(), CHECK_ORDER_TYPE, regionCode.getProvinceCode().intValue(), regionCode.getCityCode().intValue());
        if (CollectionUtils.isEmpty(worderTemplateDtoList)) {
            log.info("message save check order {} 没有对应的检查工单模板", pushCheckOrder.getSubOrderCode());
            updateMessageTypeFail(messageId, "没有对应的检查工单模板", pushCheckOrder.getSubOrderCode());
            return null;
        }
        worderTemplateDtoList.sort(Comparator.comparing(WorderTemplateDto::getTemplateId).reversed());
        return worderTemplateDtoList.get(0);
    }

    /**
     * 检查订单号是否重复
     */
    private boolean isOrderDuplicate(Integer messageId, PushCheckOrder pushCheckOrder, WorderTemplateDto worderTemplateDto) {
        if (validateCompanyOrderNumberAndBrandExsit(pushCheckOrder.getSubOrderCode(), worderTemplateDto.getTemplateId())) {
            log.info("message save check order {} 车企订单号已存在，无法创建工单", pushCheckOrder.getSubOrderCode());
            worderIntfMessageDao.updateMessageTypeById(messageId, 1, "车企订单号已存在，无法创建工单");
            return true;
        }
        return false;
    }

    /**
     * 创建工单基本信息
     */
    private WorderInfoEntity createWorderInfoEntity(PushCheckOrder pushCheckOrder, BrandEntity brandEntity, RegionCode regionCode, WorderTemplateDto worderTemplateDto) {
        WorderInfoEntity worderInfoEntity = new WorderInfoEntity();

        String address = regionCode.getProvinceCode() + "_" + regionCode.getCityCode() + "_" + regionCode.getAreaCode() + "_" + pushCheckOrder.getAddress();
        address = address.replaceAll(EMOJI_REGEX, "");

        String userName = pushCheckOrder.getContactName();
        userName = userName.replaceAll(EMOJI_REGEX, "");

        worderInfoEntity.setPushOrderWorderSource("byd");
        worderInfoEntity.setUserName(userName);
        worderInfoEntity.setUserPhone(pushCheckOrder.getContactMobile());
        worderInfoEntity.setAddress(address);
        worderInfoEntity.setCompanyOrderNumber(pushCheckOrder.getSubOrderCode());
        worderInfoEntity.setTemplateId(worderTemplateDto.getTemplateId());
        worderInfoEntity.setCarBrand(brandEntity.getId() + "");
        worderInfoEntity.setCarModel("4");
        worderInfoEntity.setCompanyId(516);
        worderInfoEntity.setPostcode("");
        worderInfoEntity.setWorderSourceTypeValue("");
        worderInfoEntity.setWorderTypeId(8);
        worderInfoEntity.setCandidate(ConstantPool.NEWS_OPERATOR_NAME);
        worderInfoEntity.setCreator(ConstantPool.NEWS_OPERATOR);

        return worderInfoEntity;
    }

    /**
     * 创建工单扩展字段
     */
    private List<WorderExtFieldEntity> createExtendedFields(PushCheckOrder pushCheckOrder, WorderInfoEntity worderInfoEntity) {
        List<WorderExtFieldEntity> worderExtFieldEntityList = new ArrayList<>();

        String dispatchTime = formatDateTime(pushCheckOrder.getDispatchTime());
        String installationCompletedTime = formatDateTime(pushCheckOrder.getInstallationCompletedTime());

        // 基础工单信息
        worderExtFieldEntityList.add(setWorderExtFieldEntity(1, "工单类型", worderInfoEntity.getWorderTypeId()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(5, "车企订单号", worderInfoEntity.getCompanyOrderNumber()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(101, "车企名称", 516));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(154, "车企派单日期", dispatchTime));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(305, "备注-创建", "检查工单"));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(306, "工单来源", ""));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(902, "客户姓名", worderInfoEntity.getUserName()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(903, "安装地址", worderInfoEntity.getAddress()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(904, "客户邮箱", ""));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(905, "客户手机", worderInfoEntity.getUserPhone()));

        // 检查工单相关字段
        worderExtFieldEntityList.add(setWorderExtFieldEntity(1676, "充电桩名称", pushCheckOrder.getWallboxName()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(1694, "充电桩功率", pushCheckOrder.getWallboxPower()));

        // 添加检查工单特有字段
        addCheckOrderSpecificFields(worderExtFieldEntityList, pushCheckOrder, installationCompletedTime);

        // 添加图片字段
        addImageFields(worderExtFieldEntityList, pushCheckOrder);

        return worderExtFieldEntityList;
    }

    /**
     * 添加检查工单特有字段
     */
    private void addCheckOrderSpecificFields(List<WorderExtFieldEntity> worderExtFieldEntityList, PushCheckOrder pushCheckOrder, String installationCompletedTime) {
        worderExtFieldEntityList.add(setWorderExtFieldEntity(3001, "子订单编号", pushCheckOrder.getSubOrderCode()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(3002, "任务序号", pushCheckOrder.getTaskSerial()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(3003, "充电桩编码", pushCheckOrder.getWallboxCode()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(3004, "质保截止日期", pushCheckOrder.getWarrantyEnd()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(3005, "安装订单号", pushCheckOrder.getInstallOrderCode()));

        worderExtFieldEntityList.add(setWorderExtFieldEntity(3006, "是否推送安装信息",
            PushCheckOrder.PushInstallation.getNameByCode(pushCheckOrder.getPushInstallation())));

        worderExtFieldEntityList.add(setWorderExtFieldEntity(3007, "安装完成时间", installationCompletedTime));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(3008, "物料编码", pushCheckOrder.getWallboxMaterialCode()));

        worderExtFieldEntityList.add(setWorderExtFieldEntity(3009, "取电方式",
            PushCheckOrder.PowerSupplyMethod.getNameByCode(pushCheckOrder.getPowerSupplyMethod())));

        worderExtFieldEntityList.add(setWorderExtFieldEntity(3010, "线缆品牌",
            PushCheckOrder.CableBrand.getNameByCode(pushCheckOrder.getCableBrand())));

        worderExtFieldEntityList.add(setWorderExtFieldEntity(3011, "线缆规格", pushCheckOrder.getCableType()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(3012, "线缆长度", pushCheckOrder.getCableLength()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(3013, "断路器品牌", pushCheckOrder.getBreakerBrand()));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(3014, "断路器型号", pushCheckOrder.getBreakerType()));

        worderExtFieldEntityList.add(setWorderExtFieldEntity(3015, "是否安装立柱",
            PushCheckOrder.YesOrNo.getNameByCode(pushCheckOrder.getInstallStake())));

        worderExtFieldEntityList.add(setWorderExtFieldEntity(3016, "是否安装保护箱",
            PushCheckOrder.YesOrNo.getNameByCode(pushCheckOrder.getInstallProtectingBox())));

        worderExtFieldEntityList.add(setWorderExtFieldEntity(3017, "是否接地极",
            PushCheckOrder.YesOrNo.getNameByCode(pushCheckOrder.getGroundElectrode())));

        worderExtFieldEntityList.add(setWorderExtFieldEntity(3018, "前端线材",
            PushCheckOrder.FrontEndCable.getNameByCode(pushCheckOrder.getFrontEndCable())));
    }

    /**
     * 添加图片字段
     */
    private void addImageFields(List<WorderExtFieldEntity> worderExtFieldEntityList, PushCheckOrder pushCheckOrder) {
        PushCheckOrderPicAttachment picAttachment = JSON.parseObject(pushCheckOrder.getPicAttrs(), PushCheckOrderPicAttachment.class);

        String constructionImage = picAttachment.getConstructionImage();
        if (StringUtils.isNotBlank(constructionImage)) {
            OtherApiResponse apiResponse = iBydApiService.getTempLink(TempLink.builder().url(constructionImage).build());
            if (apiResponse.getErrno() == 0) {
                String value = apiResponse.getData().toString();
                worderExtFieldEntityList.add(setWorderExtFieldEntity(2167, "施工使用线缆", processImageUrl(value, "施工使用线缆")));
            }
        }

        String constructionImageUrl = null;

        worderExtFieldEntityList.add(setWorderExtFieldEntity(2168, "充电桩序列码", processImageUrl(picAttachment.getSequenceImage(), "充电桩序列码")));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(2169, "用线始端", processImageUrl(picAttachment.getLineStartImage(), "用线始端")));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(2170, "用线末端", processImageUrl(picAttachment.getLineEndImage(), "用线末端")));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(2171, "接地线或接地极", processImageUrl(picAttachment.getGroundWireImage(), "接地线或接地极")));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(2172, "电源点火零电压", processImageUrl(picAttachment.getZeroVoltageImage(), "电源点火零电压")));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(2173, "人桩合照", processImageUrl(picAttachment.getManPileImage(), "人桩合照")));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(2174, "安装确认单", processImageUrl(picAttachment.getConfirmationImage(), "安装确认单")));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(2175, "增项收费单", processImageUrl(picAttachment.getIncreaseChargeImage(), "增项收费单")));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(2176, "漏保上端火零绝缘电阻", processImageUrl(picAttachment.getFireZeroResistanceImage(), "漏保上端火零绝缘电阻")));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(2177, "漏保下端零地电压", processImageUrl(picAttachment.getZeroGroundVoltageImage(), "漏保下端零地电压")));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(2178, "试充照片", processImageUrl(picAttachment.getTrialChargeImage(), "试充照片")));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(2179, "充电桩铭牌图片", processImageUrl(picAttachment.getPileNameplateImage(), "充电桩铭牌图片")));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(2080, "放弃电力报装免责声明", processImageUrl(picAttachment.getDisclaimersImage(), "放弃电力报装免责声明")));
        worderExtFieldEntityList.add(setWorderExtFieldEntity(2181, "同级负载确认书", processImageUrl(picAttachment.getLoadConfirmationImage(), "同级负载确认书")));
    }

    /**
     * 格式化日期时间
     */
    private String formatDateTime(java.util.Date date) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
        return sdf.format(date);
    }

    /**
     * 转换比亚迪区域编码
     */
    private RegionCode convertRegion(String provinceCode, String cityCode, String areaCode) {
        RegionCode regionCode = new RegionCode();

        if (StringUtils.isNotBlank(provinceCode)) {
            Long bydProvinceCode = Long.valueOf(provinceCode);
            BizRegionEntity provinceRegion = bizRegionService.getRegionByBydCode(bydProvinceCode);
            if (provinceRegion != null) {
                regionCode.setProvinceCode(provinceRegion.getId());
            }
        }

        if (StringUtils.isNotBlank(cityCode)) {
            Long bydCityCode = Long.valueOf(cityCode);
            BizRegionEntity cityRegion = bizRegionService.getRegionByBydCode(bydCityCode);
            if (cityRegion != null) {
                regionCode.setCityCode(cityRegion.getId());
            } else {
                regionCode.setCityCode(regionCode.getProvinceCode());
            }
        } else {
            regionCode.setCityCode(regionCode.getProvinceCode());
        }

        if (StringUtils.isNotBlank(areaCode)) {
            Long bydAreaCode = Long.valueOf(areaCode);
            BizRegionEntity areaRegion = bizRegionService.getRegionByBydCode(bydAreaCode);
            if (areaRegion != null) {
                regionCode.setAreaCode(areaRegion.getId());
            } else {
                regionCode.setAreaCode(regionCode.getCityCode());
            }
        } else {
            regionCode.setAreaCode(regionCode.getCityCode());
        }
        return regionCode;
    }

    private Boolean validateCompanyOrderNumberAndBrandExsit(String companyOrderNumber, Integer templateId) {
        return worderInformationService.validateCompanyOrderNumberAndBrandExsit(companyOrderNumber, templateId);
    }

    private WorderExtFieldEntity setWorderExtFieldEntity(Integer fieldId, String fieldName, Object fieldValue) {
        String value = "";
        if (fieldValue != null) {
            value = String.valueOf(fieldValue);
        }
        return WorderExtFieldEntity.builder()
                .fieldId(fieldId)
                .fieldName(fieldName)
                .fieldValue(value).build();
    }

    public R saveWorderInformation(WorderInfoEntity worderInfoEntity) {
        log.info("保存检查工单信息 {}", JSON.toJSONString(worderInfoEntity));
        return worderInformationService.saveWorderInformationByServiceProvider(worderInfoEntity);
    }

    private WorderInformationEntity getWorderInformationByWorderNo(String worderNo) {
        QueryWrapper<WorderInformationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("worder_no", worderNo);
        return worderInformationService.getOne(queryWrapper);
    }

    private void goAutoSendWorder(String worderNo) {
        try {
            worderInformationService.goAutoSendWorder(worderNo, "系统自动", null);
            // 修改工单状态
            worderInformationService.updateWorderStatus(worderNo);
        } catch (Exception e) {
            log.error("检查工单自动派单异常, worderNo: {}", worderNo, e);
        }
    }

    /**
     * 处理图片URL，下载图片并上传到OSS，返回fileId
     * 参考 SysFileController#imageUpload 方法的实现
     * @param imageUrl 图片URL
     * @param fieldName 字段名称，用于日志记录
     * @return fileId，如果处理失败返回null
     */
    private Integer processImageUrl(String imageUrl, String fieldName) {
        if (StringUtils.isBlank(imageUrl)) {
            return null;
        }

        try {
            // 从URL下载图片
            URL url = new URL(imageUrl);
            URLConnection connection = url.openConnection();
            connection.setConnectTimeout(CONNECTION_TIMEOUT);
            connection.setReadTimeout(READ_TIMEOUT);
            connection.setRequestProperty("User-Agent", USER_AGENT);

            try (InputStream inputStream = connection.getInputStream()) {
                // 获取文件名和扩展名
                String fileName = extractFileName(imageUrl);

                // 读取图片数据 - 使用兼容的方式读取所有字节
                byte[] imageData = org.apache.commons.io.IOUtils.toByteArray(inputStream);

                // 创建MultipartFile对象
                MultipartFile multipartFile = new MockMultipartFile(
                    fileName,
                    fileName,
                    determineContentType(fileName),
                    new ByteArrayInputStream(imageData)
                );

                return uploadImageFile(multipartFile, fieldName);
            }
        } catch (Exception e) {
            log.error("处理图片失败, fieldName: {}, imageUrl: {}", fieldName, imageUrl, e);
            return null;
        }
    }

    /**
     * 提取文件名
     */
    private String extractFileName(String imageUrl) {
        String fileName = imageUrl.substring(imageUrl.lastIndexOf("/") + 1);
        if (fileName.contains("?")) {
            fileName = fileName.substring(0, fileName.indexOf("?"));
        }
        if (!fileName.contains(".")) {
            fileName = fileName + DEFAULT_IMAGE_EXTENSION;
        }
        return fileName;
    }

    /**
     * 确定文件类型
     */
    private String determineContentType(String fileName) {
        if (!fileName.contains(".")) {
            return "image/jpeg";
        }
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        switch (extension) {
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "bmp":
                return "image/bmp";
            case "webp":
                return "image/webp";
            default:
                return "image/jpeg";
        }
    }

    /**
     * 上传图片文件
     */
    private Integer uploadImageFile(MultipartFile multipartFile, String fieldName) {
        try {
            long size = multipartFile.getSize();
            if (size > 0) {
                String oldName = multipartFile.getOriginalFilename();
                Map<String, String> urlMap = FileUtils.getUrl(multipartFile);
                String path = urlMap.get("url");
                String name = urlMap.get("name");
                String md5Hex = urlMap.get("md5Str");
                Integer fileId = sysFilesService.saveFilePath(path, name, oldName, md5Hex);

                log.info("成功处理图片, fieldName: {}, fileId: {}, size: {} bytes", fieldName, fileId, size);
                return fileId;
            } else {
                log.warn("图片大小为0, fieldName: {}", fieldName);
                return null;
            }
        } catch (Exception e) {
            log.error("上传图片文件失败, fieldName: {}", fieldName, e);
            return null;
        }
    }

}
