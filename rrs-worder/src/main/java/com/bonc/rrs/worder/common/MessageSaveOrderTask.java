package com.bonc.rrs.worder.common;

import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.rrs.byd.domain.PushOrderBody;
import com.bonc.rrs.byd.enums.ConstantPool;
import com.bonc.rrs.byd.response.PushApiResponse;
import com.bonc.rrs.serviceprovider.po.RegionCode;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.util.SmsUtil;
import com.bonc.rrs.worder.dao.WorderInformationAttributeDao;
import com.bonc.rrs.worder.dao.WorderIntfMessageDao;
import com.bonc.rrs.worder.entity.*;
import com.bonc.rrs.worder.entity.dto.WorderTemplateDto;
import com.bonc.rrs.worder.service.BizRegionService;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.worder.service.WorderRemarkLogService;
import com.bonc.rrs.worder.service.WorderTemplateService;
import com.bonc.rrs.xk.service.XkApiService;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.constant.WarningConstant;
import com.youngking.lenmoncore.common.utils.R;
import com.youngking.renrenwithactiviti.modules.job.task.ITask;
import com.youngking.renrenwithactiviti.modules.sys.entity.BrandEntity;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.BrandService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.subject.SimplePrincipalCollection;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.util.ThreadContext;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 推送订单入库定时任务
 * @Date 2024/04/3 10:06
 */
@Slf4j
@Component("messageSaveOrderTask")
public class MessageSaveOrderTask implements ITask {

    @Autowired
    private XkApiService xkApiService;
    @Autowired
    private WorderIntfMessageDao worderIntfMessageDao;
    @Autowired
    private BrandService brandService;
    @Autowired
    private BizRegionService bizRegionService;
    @Autowired
    private WorderTemplateService worderTemplateService;
    @Autowired
    private WorderInformationService worderInformationService;
    @Autowired
    private WorderInformationAttributeDao worderInformationAttributeDao;
    @Autowired
    private WorderRemarkLogService worderRemarkLogService;

    private void createDefaultLoginUser() {
        DefaultWebSecurityManager defaultWebSecurityManager = new DefaultWebSecurityManager();
        SecurityUtils.setSecurityManager(defaultWebSecurityManager);

        // 由于跳过登陆认证，需要设置固定的登陆信息
        SysUserEntity sysUserEntity = new SysUserEntity();
        sysUserEntity.setUserId(89L);
        sysUserEntity.setUsername("系统自动");
        // 创建一个Subject.Builder
        Subject.Builder builder = new Subject.Builder();
        // 设置身份信息
        PrincipalCollection principals = new SimplePrincipalCollection(sysUserEntity, "系统自动");
        builder.principals(principals);
        // 设置是否已经认证
        builder.authenticated(true);
        // 创建Subject实例
        Subject subject = builder.buildSubject();
        ThreadContext.bind(subject);
    }

    @Override
    @Lock4j(expire = 330000)
    public void run(String params) {
        log.info("推送订单入库定时任务 ---- 开始");
        // 825 上海飞隼科技有限公司
        List<WorderIntfMessageEntity> worderIntfMessageEntityList = worderIntfMessageDao.queryNotSaveOrder(0);

        if (worderIntfMessageEntityList != null) {
            createDefaultLoginUser();
            for (WorderIntfMessageEntity worderIntfMessageEntity : worderIntfMessageEntityList) {
                log.info("message save order {} start.", worderIntfMessageEntity.getOrderCode());
                try {
                    PushOrderBody pushOrderBody = JSON.parseObject(worderIntfMessageEntity.getData(), PushOrderBody.class);
                    saveOrder(worderIntfMessageEntity.getId(), pushOrderBody);
                    //仰望品牌进单就给王彩霞发短信，手机号码：13698678607
                    if (pushOrderBody.getCarBrand().equals("30")) {
                        SmsUtil.sendSms("13698678607", "接到仰望订单 车企订单号" + pushOrderBody.getOrderCode(), "【到每家科技服务】");
                    }
                } catch (Exception e) {
                    updateMessageTypeFail(worderIntfMessageEntity.getId(), e.toString());
                }

                log.info("message save order {} end", worderIntfMessageEntity.getOrderCode());
            }
        }
        log.info("推送订单入库定时任务 ---- 结束");
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateWorderId(Integer messageId, Integer worderId) {
        worderIntfMessageDao.updateWorderIdById(messageId, worderId);
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateMessageTypeFail(Integer messageId, String errorMsg) {
        worderIntfMessageDao.updateMessageTypeById(messageId, 2, errorMsg);
        SmsUtil.sendSms("15910305046", "推送比亚迪订单入库失败", "【到每家科技服务】");
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateMessageTypeFail(Integer messageId, String errorMsg, String companyOrderNumber) {
        worderIntfMessageDao.updateMessageTypeById(messageId, 2, errorMsg);
        SmsUtil.sendSms("15910305046", "推送比亚迪订单入库失败,车企订单号:" + companyOrderNumber, "【到每家科技服务】");
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateMessageTypeRepeatWorder(Integer messageId, String errorMsg) {
        worderIntfMessageDao.updateMessageTypeById(messageId, 1, errorMsg);
    }

    private void saveOrder(Integer messageId, PushOrderBody pushOrderBody) {
        R r = null;
        try {
            BrandEntity brandEntity = brandService.queryBrandByCompanyBrandId(pushOrderBody.getCarBrand());
            if (brandEntity == null) {
                log.info("message save order " + pushOrderBody.getOrderCode() + " 非法品牌");
                updateMessageTypeFail(messageId, "非法品牌");
                return;
            }

            RegionCode regionCode = convertRegion(pushOrderBody.getProvinceCode(), pushOrderBody.getCityCode(), pushOrderBody.getAreaCode());

            if (regionCode.getProvinceCode() == null || regionCode.getCityCode() == null || regionCode.getAreaCode() == null) {
                log.info("message save order " + pushOrderBody.getOrderCode() + " 地区不匹配");
                updateMessageTypeFail(messageId, "地区不匹配");
                return;
            }

            List<WorderTemplateDto> worderTemplateDtoList = worderTemplateService.findTemplateInfoByBrandIdAndWorderTypeIdAndRegion(brandEntity.getId(), 5, regionCode.getProvinceCode().intValue(), regionCode.getCityCode().intValue());
            if (CollectionUtils.isEmpty(worderTemplateDtoList)) {
                log.info("message save order " + pushOrderBody.getOrderCode() + " 没有对应的工单模板");
                updateMessageTypeFail(messageId, "没有对应的工单模板");
                return;
            }
            worderTemplateDtoList.sort(Comparator.comparing(WorderTemplateDto::getTemplateId).reversed());
            WorderTemplateDto worderTemplateDto = worderTemplateDtoList.get(0);

            if (validateCompanyOrderNumberAndBrandExsit(pushOrderBody.getOrderCode(), worderTemplateDto.getTemplateId())) {
                log.info("message save order " + pushOrderBody.getOrderCode() + " 车企订单号已存在，无法创建工单");
                updateMessageTypeRepeatWorder(messageId, "车企订单号已存在，无法创建工单");
                return;
            }

            // 匹配表情符
            String regex = "([\\u20A0-\\u32FF\\uD83C-\\uDFFF\\u2600-\\u27FF])|([\\uD830-\\uD83F][\\uDC00-\\uDFFF])";

            Integer companyId = 516;

            WorderInfoEntity worderInfoEntity = new WorderInfoEntity();

            String address = regionCode.getProvinceCode() + "_" + regionCode.getCityCode() + "_" + regionCode.getAreaCode() + "_" + pushOrderBody.getContactAddress();
            address = address.replaceAll(regex, "");

            String userName = pushOrderBody.getContactName();
            userName = userName.replaceAll(regex, "");

            String dispatchTime = pushOrderBody.getDispatchTime();
            String vin = pushOrderBody.getVin();
            String contactRemark = "";
            if (StringUtils.isNotBlank(pushOrderBody.getContactRemark())) {
                contactRemark = pushOrderBody.getContactRemark().replaceAll(regex, "");
            }
            String gunLineLength = pushOrderBody.getGunLineLength();
            String wallboxName = pushOrderBody.getWallboxName();
            String wallboxPower = pushOrderBody.getWallboxPower();

            String bringWallbox = ConstantPool.BringWallbox.getNameByCode(pushOrderBody.getBringWallbox());
            if (StringUtils.equals(bringWallbox, "否")) {
                userName += "-不带桩";
            }

            String financialAttributionCode = pushOrderBody.getType();
            String carOwnerTypeCode = pushOrderBody.getCarOwnerType();
            String financialAttributionName = financialAttributionCode;
            if (StringUtils.isNotBlank(financialAttributionCode)) {
                financialAttributionName = ConstantPool.FinancialAttribution.getNameByCode(financialAttributionCode);
            }

            String carOwnerTypeName = carOwnerTypeCode;
            if (StringUtils.isNotBlank(carOwnerTypeCode)) {
                carOwnerTypeName = ConstantPool.CarOwnerType.getNameByCode(carOwnerTypeCode);
            }

            String bydCarSeries = pushOrderBody.getCarSeries();
            String bydCarModel = pushOrderBody.getCarModel();

            //11、12：一级风险；
            //21、22、23、24：二级风险；31、32、33：三级风险；
            //41、42、43、44、45：四级风险
            String riskGradeCode = pushOrderBody.getRiskGrade();
            String riskGrade = RiskGrade.getValueByCode(riskGradeCode);
            if (StringUtils.isNotBlank(riskGrade)) {
                userName += "-"+riskGrade;
            }
            //风险原因
            String riskReason = pushOrderBody.getRiskReason();

            worderInfoEntity.setPushOrderWorderSource("byd");
            worderInfoEntity.setUserName(userName);
            worderInfoEntity.setUserPhone(pushOrderBody.getContactMobile());
            worderInfoEntity.setAddress(address);
            worderInfoEntity.setCompanyOrderNumber(pushOrderBody.getOrderCode());
            worderInfoEntity.setTemplateId(worderTemplateDto.getTemplateId());

            worderInfoEntity.setCarBrand(brandEntity.getId() + "");
            worderInfoEntity.setCarModel("4");
            worderInfoEntity.setCompanyId(companyId);

            worderInfoEntity.setPostcode("");
            worderInfoEntity.setWorderSourceTypeValue("");
            worderInfoEntity.setWorderTypeId(5);

            worderInfoEntity.setCandidate(ConstantPool.NEWS_OPERATOR_NAME);
            worderInfoEntity.setCreator(ConstantPool.NEWS_OPERATOR);

            List<WorderExtFieldEntity> worderExtFieldEntityList = new ArrayList<>();

            worderExtFieldEntityList.add(setWorderExtFieldEntity(1, "工单类型", worderInfoEntity.getWorderTypeId()));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(5, "车企订单号", worderInfoEntity.getCompanyOrderNumber()));

            worderExtFieldEntityList.add(setWorderExtFieldEntity(101, "车企名称", companyId));

            worderExtFieldEntityList.add(setWorderExtFieldEntity(154, "车企派单日期", dispatchTime));

            worderExtFieldEntityList.add(setWorderExtFieldEntity(153, "VIN 车架号", vin));

            worderExtFieldEntityList.add(setWorderExtFieldEntity(305, "备注-创建", contactRemark));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(306, "工单来源", ""));

            worderExtFieldEntityList.add(setWorderExtFieldEntity(902, "客户姓名", userName));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(903, "安装地址", address));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(904, "客户邮箱", ""));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(905, "客户手机", worderInfoEntity.getUserPhone()));

            worderExtFieldEntityList.add(setWorderExtFieldEntity(1667, "枪线长度", gunLineLength));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(1676, "充电桩名称", wallboxName));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(1678, "是否带桩上门", bringWallbox));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(1679, "订单类型（选）", carOwnerTypeName));

            worderExtFieldEntityList.add(setWorderExtFieldEntity(1680, "财务归属", financialAttributionName));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(1684, "比亚迪车系", bydCarSeries));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(1686, "比亚迪车型", bydCarModel));

            worderExtFieldEntityList.add(setWorderExtFieldEntity(1694, "充电桩功率", wallboxPower));

            //TODO 风险等级，风险原因
            worderExtFieldEntityList.add(setWorderExtFieldEntity(2095, "风险等级", riskGrade));
            worderExtFieldEntityList.add(setWorderExtFieldEntity(2096, "风险原因", riskReason));

            worderInfoEntity.setWorderExtFieldList(worderExtFieldEntityList);

            r = saveWorderInformation(worderInfoEntity);
        } catch (Exception e) {
            log.error("message save order " + pushOrderBody.getOrderCode() + " 出现异常", e);
            updateMessageTypeFail(messageId, "推送安装订单基本安装信息给指定供应商接口下单失败，" + e.toString(), pushOrderBody.getOrderCode());
            return;
        }

        if (!IntegerEnum.ZERO.getValue().equals(r.get(WarningConstant.CODE))) {
            log.info("message save order " + pushOrderBody.getOrderCode() + r.get("msg"));
            updateMessageTypeFail(messageId, r.get("msg") + "");
            return;
        }
        String worderNo = r.get("worderNo") + "";
        WorderInformationEntity worderInformationEntity = getWorderInformationByWorderNo(worderNo);

        updateWorderId(messageId, worderInformationEntity.getWorderId());
        // 自动派单
        Integer autoSendDotId = goAutoSendWorder(worderNo, ConstantPool.NEWS_OPERATOR_NAME);
        if (autoSendDotId != null && isXkTransferWorderDot(autoSendDotId)) {
            PushApiResponse xkResponse = xkApiService.pushOrder(pushOrderBody);

            SimpleDateFormat formatDate = new SimpleDateFormat("yyyy-MM-dd");
            String title = formatDate.format(new Date()) + " " + ConstantPool.NEWS_OPERATOR_NAME + "小咖转单";
            if (ConstantPool.SUCCESS.equals(xkResponse.getMessage())) {
                // 保存转单标识
                saveWorderAttribute(worderInformationEntity.getWorderId(), "Transfer-Order", "转单标识", "1", "TransferOrder");
                worderIntfMessageDao.updateTransfer(worderInformationEntity.getWorderId(), 1);
                saveWorderRemarkLog(worderInformationEntity.getWorderNo(), "安装订单信息更新推送服务商接口转单推送小咖完成", title, new Date());
            } else {
                saveWorderRemarkLog(worderInformationEntity.getWorderNo(), "安装订单信息更新推送服务商接口转单推送小咖失败:" + xkResponse.getMessage(), title, new Date());
            }
        }
    }

    /**
     * 转换比亚迪区域编码
     *
     * @param provinceCode
     * @param cityCode
     * @param areaCode
     * @return
     */
    private RegionCode convertRegion(String provinceCode, String cityCode, String areaCode) {
        RegionCode regionCode = new RegionCode();
        if (StringUtils.isNotBlank(provinceCode)) {
            Long bydProvinceCode = Long.valueOf(provinceCode);
            BizRegionEntity provinceRegion = bizRegionService.getRegionByBydCode(bydProvinceCode);
            if (IntegerEnum.ONE.getValue().equals(provinceRegion.getType())) {
                regionCode.setProvinceCode(provinceRegion.getId());
            } else if (IntegerEnum.TWO.getValue().equals(provinceRegion.getType())) {
                regionCode.setProvinceCode(provinceRegion.getPid());
                regionCode.setCityCode(provinceRegion.getId());
            } else if (IntegerEnum.THIRD.getValue().equals(provinceRegion.getType())) {
                regionCode.setCityCode(provinceRegion.getPid());
                regionCode.setAreaCode(provinceRegion.getId());
                BizRegionEntity cityRegion = bizRegionService.getById(provinceRegion.getPid());
                regionCode.setProvinceCode(cityRegion.getPid());
            }
        }

        if (StringUtils.isNotBlank(cityCode)) {
            Long bydCityCode = Long.valueOf(cityCode);
            BizRegionEntity cityRegion = bizRegionService.getRegionByBydCode(bydCityCode);
            if (IntegerEnum.TWO.getValue().equals(cityRegion.getType())) {
                regionCode.setCityCode(cityRegion.getId());
            } else if (IntegerEnum.THIRD.getValue().equals(cityRegion.getType())) {
                regionCode.setCityCode(cityRegion.getPid());
                regionCode.setAreaCode(cityRegion.getId());
            }
        }

        // 区编码 未获取到数据 填写市编码
        if (StringUtils.isNotBlank(areaCode)) {
            Long bydAreaCode = Long.valueOf(areaCode);
            BizRegionEntity areaRegion = bizRegionService.getRegionByBydCode(bydAreaCode);
            if (areaRegion != null) {
                regionCode.setAreaCode(areaRegion.getId());
            } else {
                regionCode.setAreaCode(regionCode.getCityCode());
            }
        } else {
            regionCode.setAreaCode(regionCode.getCityCode());
        }
        return regionCode;
    }

    private Boolean validateCompanyOrderNumberAndBrandExsit(String compayOrderNumber, Integer templateId) {
        return worderInformationService.validateCompanyOrderNumberAndBrandExsit(compayOrderNumber, templateId);
    }

    private WorderExtFieldEntity setWorderExtFieldEntity(Integer fieldId, String fieldName, Object fieldValue) {
        String value = "";
        if (fieldValue != null) {
            value = String.valueOf(fieldValue);
        }
        return WorderExtFieldEntity.builder()
                .fieldId(fieldId)
                .fieldName(fieldName)
                .fieldValue(value).build();
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public R saveWorderInformation(WorderInfoEntity worderInfoEntity) {
        log.info("保存工单信息 {}", JSON.toJSONString(worderInfoEntity));
        return worderInformationService.saveWorderInformationByServiceProvider(worderInfoEntity);
    }

    private WorderInformationEntity getWorderInformationByWorderNo(String worderNo) {
        if (StringUtils.isBlank(worderNo)) {
            return null;
        }
        List<WorderInformationEntity> worderInformationEntityList = worderInformationService.getBaseMapper().selectList(new QueryWrapper<WorderInformationEntity>().eq("worder_no", worderNo));
        if (worderInformationEntityList == null || worderInformationEntityList.size() == 0) {
            return null;
        }
        return worderInformationEntityList.get(0);
    }

    /**
     * 自动派单
     *
     * @param worderNo
     * @param username
     */
    private Integer goAutoSendWorder(String worderNo, String username) {
        Results results = worderInformationService.goAutoSendWorder(worderNo, username, null);
        // 修改工单状态 0
        worderInformationService.updateWorderStatus(worderNo);
        return results.getDotId();
    }

    private Boolean isXkTransferWorderDot(Integer dotId) {
        // 825 上海飞隼科技有限公司
        if (dotId != null && dotId == 825) {
            return true;
        }
        return false;
    }

    private void saveWorderAttribute(Integer worderId,
                                     String attributeCode,
                                     String attributeName,
                                     String attributeValue,
                                     String attribute) {
        WorderInformationAttributeEntity entity = new WorderInformationAttributeEntity();
        entity.setWorderId(worderId);
        entity.setAttributeCode(attributeCode);
        entity.setAttributeName(attributeName);
        entity.setAttributeValue(attributeValue);
        entity.setAttribute(attribute);
        worderInformationAttributeDao.insert(entity);
    }

    /**
     * 保存备注
     *
     * @param worderNo
     * @param content
     * @param title
     * @param createTime
     */
    private void saveWorderRemarkLog(String worderNo, String content, String title, Date createTime) {
        WorderRemarkLogEntity worderRemarkLog = new WorderRemarkLogEntity();
        worderRemarkLog.setWorderNo(worderNo);   //工单编号
        worderRemarkLog.setContent(content);  //备注内容
        worderRemarkLog.setCreateTime(createTime);
        worderRemarkLog.setTitle(title);
        worderRemarkLogService.save(worderRemarkLog);
    }

    @Getter
    private enum RiskGrade {
        //11、12：一级风险；
        //21、22、23、24：二级风险；31、32、33：三级风险；
        //41、42、43、44、45：四级风险
        ONE_ONE("11", "一级"),
        ONE_TWO("12", "一级"),
        TWO_ONE("21", "二级"),
        TWO_TWO("22", "二级"),
        TWO_THREE("23", "二级"),
        TWO_FOUR("24", "二级"),
        THREE_ONE("31", "三级"),
        THREE_TWO("32", "三级"),
        THREE_THREE("33", "三级"),
        FOUR_ONE("41", "四级"),
        FOUR_TWO("42", "四级"),
        FOUR_THREE("43", "四级"),
        FOUR_FOUR("44", "四级"),
        FOUR_FIVE("45", "四级");


        private final String code;
        private final String value;
        RiskGrade(String code, String value) {
            this.code = code;
            this.value = value;
        }

        public static String getValueByCode(String code) {
            for (MessageSaveOrderTask.RiskGrade riskGrade : MessageSaveOrderTask.RiskGrade.values()) {
                if (riskGrade.getCode().equals(code)) {
                    return riskGrade.getValue();
                }
            }
            return null;
        }
    }
}
