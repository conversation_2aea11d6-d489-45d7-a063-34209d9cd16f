package com.bonc.rrs.byd.service;

import com.bonc.rrs.byd.domain.*;
import com.bonc.rrs.byd.response.OtherApiResponse;

/**
 * 比亚迪对接Service接口
 *
 * <AUTHOR>
 * @date 2024-02-26
 */
public interface IBydApiService {
    /**
     * 服务商联系信息回传
     *
     * @param pushContactInfo 服务商联系信息
     * @return
     */
    OtherApiResponse pushContactInfo(PushContactInfo pushContactInfo);

    /**
     * 服务商勘测信息回传
     *
     * @param pushSurveyInfo 服务商勘测信息
     * @return
     */
    OtherApiResponse pushSurveyInfo(PushSurveyInfo pushSurveyInfo);

    /**
     * 服务商勘测信息回传-老
     *
     * @param pushSurveyInfoOld 服务商勘测信息
     * @return
     */
    @Deprecated
    OtherApiResponse pushSurveyInfoOld(PushSurveyInfoOld pushSurveyInfoOld);

    /**
     * 服务商提交审核
     *
     * @param pushSubmitReviewInfo 服务商提交审核信息
     * @return
     */
    OtherApiResponse pushSubmitReviewInfo(PushSubmitReviewInfo pushSubmitReviewInfo);

    /**
     * 服务商提交审核
     *
     * @param pushReviewInfo 服务商提交审核信息
     * @return
     */
    OtherApiResponse pushReviewInfo(PushReviewInfo pushReviewInfo);


    /**
     * 服务商售后信息回传
     *
     * @param pushProcessingInfo 服务商售后信息
     * @return
     */
    OtherApiResponse pushProcessing(PushProcessingInfo pushProcessingInfo);
    /**
     * 服务商取消审核信息回传
     * @param pushSubmitInfo 服务商取消审核信息回传
     * @return
     */
    OtherApiResponse pushSubmitInfo(PushSubmitInfo pushSubmitInfo);

    /**
     * 服务商安装信息回传
     *
     * @param pushInstallationInfo 服务商安装信息
     * @return
     */
    OtherApiResponse pushInstallationInfo(PushInstallationInfo pushInstallationInfo);

    /**
     * 服务商安装信息回传
     *
     * @param pushInstallationInfo 服务商安装信息
     * @return
     */
    @Deprecated
    OtherApiResponse pushInstallationInfoOld(PushInstallationInfoOld pushInstallationInfo);

    /**
     * 服务商安装附件信息回传
     *
     * @param pushAccessoriesInfo 服务商安装附件信息
     * @return
     */
    OtherApiResponse pushAccessoriesInfo(PushAccessoriesInfo pushAccessoriesInfo);

    /**
     * 服务商安装附件信息回传-老
     *
     * @param pushAccessoriesInfoOld 服务商安装附件信息
     * @return
     */
    @Deprecated
    OtherApiResponse pushAccessoriesInfoOld(PushAccessoriesInfoOld pushAccessoriesInfoOld);

    /**
     * 服务商安装附件信息回传
     *
     * @param pushRepairAccessoriesInfo 服务商安装附件信息
     * @return
     */
    OtherApiResponse pushRepairAccessoriesInfo(PushRepairAccessoriesInfo pushRepairAccessoriesInfo);

    /**
     * 服务商报修联系信息回传
     * @param aoPushContactInfo
     * @return
     */
    OtherApiResponse aoPushContactInfo(AoPushContactInfo aoPushContactInfo);

    /**
     * 服务商消息已读状态回传
     */
    OtherApiResponse pushNotifyReadRecord(PushNotifyReadRecord pushNotifyReadRecord, String orderCode);

    /**
     * 服务商安装订单信息网点记录回传
     */
    OtherApiResponse pushBranchRecord(PushBranchRecord pushBranchRecord);

    /**
     * 服务商维修订单信息网点记录回传
     */
    OtherApiResponse aoPushBranchRecord(PushBranchRecord pushBranchRecord);

    /*
    * 服务商报修信息回传--新
     */
    OtherApiResponse pushRepairAccessoriesInfoNew(RepairAfterSalesInfo repairAfterSalesInfo);

    /*
     * 服务商上传附件
     */
    OtherApiResponse fileUpload(PushFile pushFile);

    /*
     * 服务商获取短链
     */
    OtherApiResponse getTempLink(TempLink tempLink);

    /**
     * 服务商回传拆桩信息
     *
     * @param pushUninstallInfo 服务商拆桩信息
     * @return
     */
    OtherApiResponse pushUninstallInfo(PushUninstallInfo pushUninstallInfo);

    /**
     * 服务商回传移桩安装信息
     * @param pushMoveInstallInfo
     * @return
     */
    OtherApiResponse pushMoveInstallInfo(PushMoveInstallInfo pushMoveInstallInfo);

    // ==================== 检查订单相关接口 ====================

    /**
     * 服务商回传检查任务联系信息
     * @param pushCheckContactInfo 检查任务联系信息
     * @return
     */
    OtherApiResponse pushCheckContactInfo(PushCheckContactInfo pushCheckContactInfo);

    /**
     * 服务商回传检查信息
     * @param pushCheckProcessing 检查信息
     * @return
     */
    OtherApiResponse pushCheckProcessing(PushCheckProcessing pushCheckProcessing);

    /**
     * 服务商检查订单提交审核
     * @param pushCheckReviewInfo 检查订单提交审核信息
     * @return
     */
    OtherApiResponse pushCheckReviewInfo(PushCheckReviewInfo pushCheckReviewInfo);

    /**
     * 服务商取消订单审核信息回传
     * @param pushCheckCancelReviewInfo 取消订单审核信息
     * @return
     */
    OtherApiResponse pushCheckCancelReviewInfo(PushCheckCancelReviewInfo pushCheckCancelReviewInfo);

    // ==================== 2.15 接口 ====================

    OtherApiResponse pushMovePileInstallSocket(PushMovePileInstallSocket pushMovePileInstallSocket);
}
