package com.bonc.rrs.serviceprovider.po;

import com.bonc.rrs.byd.domain.*;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/2/26 18:31
 */
@Data
@Builder
public class BusinessProcessPo {
    /**
     * 工单id
     */
    private Integer worderId;

    /**
     * 工单号
     */
    private String worderNo;

    /**
     * 审核类型  1、审核通过 2、审核不通过
     */
    private String auditType;

    private String operator;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 预计上门勘测时间
     */
    private String conveyAppointTime;

    /**
     * 预计上门安装时间
     */
    private String installAppointTime;

    /**
     * 工单类型
     */
    private Integer worderTypeId;

    /**
     * 订单来源
     */
    private String orderSouce;

    /**
     * 是否小咖转单
     */
    private Boolean isXkRemoteOrder = false;
    /**
     * 推送安装订单基本安装信息
     */
    private ParamBody paramBody;

    /**
     * 推送安装订单基本安装信息
     */
    private PushRepairOrderBody pushRepairOrderBody;

    /**
     * 安装订单信息更新推送服务商服务商
     */
    private PushUpdateOrder pushUpdateOrder;

    /**
     * 取消工单
     */
    private PushCancelOrder pushCancelOrder;

    /**
     * 关闭工单
     */
    private PushCloseOrder pushCloseOrder;

    /**
     * 联系信息通知
     */
    private PushContactInfo pushContactInfo;

    /**
     * 厂端提交审核信息
     */
    private PushSubmitInfo pushSubmitInfo;

    private PushSettleInfo pushSettleInfo;

    /**
     * 勘测回传信息
     */
    private PushSurveyInfo pushSurveyInfo;

    /**
     * CPIM推送消息通知给指定供应商接口
     */
    private PushNotifyMsg pushNotifyMsg;

    private PushBranchRecord pushBranchRecord;

    /**
     * 服务商报修订单售后信息
     */
    private RepairAfterSalesInfo repairAfterSalesInfo;

    /**
     * 索赔订单费用
     */
    private ClaimOrderFeeDto claimOrderFee;

    /**
     * 拆桩信息
     */
    private PushUninstallInfo pushUninstallInfo;

    /**
     * 移桩安装信息
     */
    private PushMoveInstallInfo pushMoveInstallInfo;

    /**
     * 检查订单信息
     */
    private PushCheckOrderBody pushCheckOrderBody;
}
