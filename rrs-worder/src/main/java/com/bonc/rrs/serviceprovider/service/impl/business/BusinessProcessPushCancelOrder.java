package com.bonc.rrs.serviceprovider.service.impl.business;

import com.bonc.rrs.byd.domain.PushCancelOrder;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.response.PushApiResponse;
import com.bonc.rrs.byd.service.IBydApiService;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.xk.service.XkApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

/**
 * 推送小咖
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class BusinessProcessPushCancelOrder extends AbstractBusinessProcess {

    final XkApiService xkApiService;

    @Override
    public String getProcessCode() {
        return "pushCancelOrder";
    }


    @Override
    public Result callBusinessProcess(BusinessProcessPo businessProcessPo) {
        PushCancelOrder pushCancelOrder = businessProcessPo.getPushCancelOrder();

        PushApiResponse pushApiResponse = xkApiService.pushCancelOrder(pushCancelOrder);

        return Result.pushApiResponse(pushApiResponse);
    }

}
