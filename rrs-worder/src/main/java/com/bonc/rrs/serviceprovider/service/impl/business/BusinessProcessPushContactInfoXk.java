package com.bonc.rrs.serviceprovider.service.impl.business;

import com.bonc.rrs.byd.domain.PushContactInfo;
import com.bonc.rrs.byd.enums.ConstantPool;
import com.bonc.rrs.byd.response.OtherApiResponse;
import com.bonc.rrs.byd.service.IBydApiService;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.worder.common.FlowCommon;
import com.bonc.rrs.worder.dao.WorderInformationDao;
import com.bonc.rrs.worder.entity.WorderInformationEntity;
import com.bonc.rrs.worder.entity.WorderRemarkLogEntity;
import com.bonc.rrs.worder.service.WorderRemarkLogService;
import com.bonc.rrs.worderapp.constant.Constant;
import com.youngking.lenmoncore.common.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 服务商业务-服务商联系信息回传
 * @Date 2024/2/26 18:04
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BusinessProcessPushContactInfoXk extends AbstractBusinessProcess {

    final WorderRemarkLogService worderRemarkLogService;

    final IBydApiService iBydApiService;

    final WorderInformationDao worderInformationDao;

    final FlowCommon flowCommon;

    @Override
    public String getProcessCode() {
        return "pushContactInfoXk";
    }

    @Transactional
    @Override
    public Result callBusinessProcess(BusinessProcessPo businessProcessPo) {
        PushContactInfo pushContactInfo = businessProcessPo.getPushContactInfo();
        if (StringUtils.isBlank(pushContactInfo.getOrderCode())) {
            return Result.otherApiResponseError("安装单号不能为空");
        }
        log.info("pushContactInfoXk " + pushContactInfo.getOrderCode() + " start");
        WorderInformationEntity worderInformationEntity = getWorderInformationByCompanyOrderNumber(pushContactInfo.getOrderCode());
        if (worderInformationEntity == null) {
            log.info("pushContactInfoXk " + pushContactInfo.getOrderCode() + " 非法安装单号");
            return Result.otherApiResponseError("非法安装单号");
        }

        if (StringUtils.isNotBlank(pushContactInfo.getFirstContactTime())) {
            try {
                DateUtils.stringToDate(pushContactInfo.getFirstContactTime(), DateUtils.DATE_TIME_PATTERN);
            } catch (Exception e) {
                log.info("pushContactInfoXk " + pushContactInfo.getOrderCode() + " 首次联系时间格式不正确");
                return Result.otherApiResponseError("首次联系时间格式不正确");
            }
        }

        if (StringUtils.isNotBlank(pushContactInfo.getPlanToSurveyTime())) {
            try {
                DateUtils.stringToDate(pushContactInfo.getPlanToSurveyTime(), DateUtils.DATE_TIME_PATTERN);
            } catch (Exception e) {
                log.info("pushContactInfoXk " + pushContactInfo.getOrderCode() + " 预计上门勘测时间格式不正确");
                return Result.otherApiResponseError("预计上门勘测时间格式不正确");
            }
        }
        if (StringUtils.isNotBlank(pushContactInfo.getPlanToInstallTime())) {
            try {
                DateUtils.stringToDate(pushContactInfo.getPlanToInstallTime(), DateUtils.DATE_TIME_PATTERN);
            } catch (Exception e) {
                log.info("pushContactInfoXk " + pushContactInfo.getOrderCode() + " 预计上门安装时间格式不正确");
                return Result.otherApiResponseError("预计上门安装时间格式不正确");
            }
        }

        if (2 == worderInformationEntity.getWorderStatus()
                && StringUtils.isNotBlank(pushContactInfo.getPlanToSurveyTime())
                && StringUtils.isBlank(pushContactInfo.getPlanToInstallTime())) {
            log.info("pushContactInfoXk " + pushContactInfo.getOrderCode() + " 安装环节不能修改预计上门勘测时间");
            return Result.otherApiResponseError("安装环节不能修改预计上门勘测时间");
        }

        try {

            List<WorderRemarkLogEntity> worderRemarkLogEntityList = worderRemarkLogService.queryConnectTime(worderInformationEntity.getWorderNo());
            if (StringUtils.isNotBlank(pushContactInfo.getFirstContactTime())
                    && StringUtils.isBlank(pushContactInfo.getPlanToSurveyTime())
                    && StringUtils.isBlank(pushContactInfo.getPlanToInstallTime())) {
                if (CollectionUtils.isEmpty(worderRemarkLogEntityList)) {
                    firstContact(worderInformationEntity, pushContactInfo.getFirstContactTime());
                }
                return Result.otherApiResponseSuccess();
            }

            String installAppointTime = "";
            String conveyAppointTime = "";
            if (StringUtils.isBlank(pushContactInfo.getPlanToInstallTime())
                    && StringUtils.isNotBlank(worderInformationEntity.getInstallAppointTime())) {
                pushContactInfo.setPlanToInstallTime(getDateField(worderInformationEntity.getInstallAppointTime()));
            } else {
                installAppointTime = pushContactInfo.getPlanToInstallTime();
            }

            if (StringUtils.isBlank(pushContactInfo.getPlanToSurveyTime())
                    && StringUtils.isNotBlank(worderInformationEntity.getConveyAppointTime())) {
                pushContactInfo.setPlanToSurveyTime(getDateField(worderInformationEntity.getConveyAppointTime()));
            } else {
                conveyAppointTime = pushContactInfo.getPlanToSurveyTime();
            }

            if (CollectionUtils.isEmpty(worderRemarkLogEntityList)) {
                firstContact(worderInformationEntity, pushContactInfo.getFirstContactTime());
            } else {
                pushContactInfo.setFirstContactTime(DateUtils.format(worderRemarkLogEntityList.get(0).getCreateTime(), DateUtils.DATE_TIME_PATTERN));
            }
            pushContactInfo.setOperatePerson(ConstantPool.BYD_OPERATOR_NAME);
            // 调用比亚迪通知接口
            OtherApiResponse otherApiResponse = iBydApiService.pushContactInfo(pushContactInfo);
            if (otherApiResponse.getErrno() != 0) {
                return Result.otherApiResponseError(otherApiResponse.getErrno(), "联系信息回传比亚迪失败" + otherApiResponse.getErrmsg());
            }

            // 有预计上门安装时间更新预约安装时间
            if (StringUtils.isNotBlank(installAppointTime)) {
                installAppoint(worderInformationEntity, installAppointTime);
                // 没有预计上门安装时间更新预约勘测时间
            } else if (StringUtils.isNotBlank(conveyAppointTime)) {
                conveyAppoint(worderInformationEntity, conveyAppointTime);
            }

        } catch (Exception e) {
            log.error("pushContactInfoXk " + pushContactInfo.getOrderCode() + " 出现异常", e);
            return Result.otherApiResponseError("联系信息回传失败");
        }
        log.info("pushContactInfoXk " + pushContactInfo.getOrderCode() + " end");
        return Result.otherApiResponseSuccess();
    }

    private void firstContact(WorderInformationEntity worderInformationEntity, String firstContactTime) {
        String record = ConstantPool.NEWS_OPERATOR_NAME + "首次电联用户";
        SimpleDateFormat formatDate = new SimpleDateFormat("yyyy-MM-dd");
        String title = formatDate.format(new Date()) + " " + record;
        saveWorderRemarkLog(worderInformationEntity.getWorderNo(), "小咖通知首次电联用户时间" + firstContactTime, title, DateUtils.stringToDate(firstContactTime, DateUtils.DATE_TIME_PATTERN));
        Map<String, Object> map = new HashMap<>();
        map.put("worderNo", worderInformationEntity.getWorderNo());
        map.put("firstCallTime", firstContactTime);
        worderInformationDao.updateFirstCallTime(map);
    }

    private void conveyAppoint(WorderInformationEntity worderInformationEntity, String conveyAppointTime) {
        Map<String, Object> map = new HashMap<>();
        map.put("worderNo", worderInformationEntity.getWorderNo());
        String worderStatus = null;
        String worderExecStatus = null;

        String record = ConstantPool.NEWS_OPERATOR_NAME + "预约勘测";

        SimpleDateFormat formatDate = new SimpleDateFormat("yyyy-MM-dd");
        String title = formatDate.format(new Date()) + " " + record;
        saveWorderRemarkLog(worderInformationEntity.getWorderNo(), "小咖通知预约勘测时间" + conveyAppointTime, title);

        // 小咖转单预约勘测后去勘测资料待提交
        worderStatus = Constant.CONVEY;
        worderExecStatus = Constant.CONVEY_NOT_COMMIT;

        saveOperationRecord(worderInformationEntity.getWorderNo(), worderStatus, worderExecStatus, record);
        //勘测预约
        map.put("worderStatus", worderStatus);
        map.put("worderExecStatus", worderExecStatus);   //工单执行状态改为3 ，待勘测
        map.put("conveyAppointTime", conveyAppointTime);   //修改勘测预约时间
        worderInformationDao.updateAppointTime(map);
        flowCommon.updateFlowStatus(worderInformationEntity.getWorderId(), "kanceziliaoweitijiao");
    }

    private void installAppoint(WorderInformationEntity worderInformationEntity, String installAppointTime) {
        Map<String, Object> map = new HashMap<>();
        map.put("worderNo", worderInformationEntity.getWorderNo());
        String worderStatus = null;
        String worderExecStatus = null;

        String record = ConstantPool.NEWS_OPERATOR_NAME + "预约安装";
        SimpleDateFormat formatDate = new SimpleDateFormat("yyyy-MM-dd");
        String title = formatDate.format(new Date()) + " " + record;
        saveWorderRemarkLog(worderInformationEntity.getWorderNo(), "小咖通知预约安装时间" + installAppointTime, title);

        // 小咖转单预约安装后去安装资料待提交
        worderStatus = Constant.INSTALL;
        worderExecStatus = Constant.INSTALL_NOT_COMMIT;

        saveOperationRecord(worderInformationEntity.getWorderNo(), worderStatus, worderExecStatus, record);
        //安装预约
        map.put("worderStatus", worderStatus);
        map.put("worderExecStatus", worderExecStatus);  //工单执行状态改为11，待安装
        map.put("installAppointTime", installAppointTime); //修改安装预约时间
        worderInformationDao.updateAppointTime(map);

        flowCommon.updateFlowStatus(worderInformationEntity.getWorderId(), "anzhuangziliaoweitijiao");

    }

    private String getDateField(String date) {
        if (StringUtils.isNotEmpty(date) && date.length() >= 12) {
            if (date.length() == 19) {
                return date;
            } else if (date.length() > 19) {
                return date.substring(0, 19);
            } else if (date.length() > 12 && date.length() < 19) {
                date = date.substring(0, 13);
            }
            return DateUtils.format(DateUtils.stringToDate(date, "yyyy-MM-dd HH"), "yyyy-MM-dd HH:59:59");
        } else if (StringUtils.isNotEmpty(date) && date.length() >= 10 && date.length() < 12) {
            if (date.length() > 10) {
                date = date.substring(0, 10);
            }
            return DateUtils.format(DateUtils.stringToDate(date, "yyyy-MM-dd"), "yyyy-MM-dd 23:59:59");
        }
        return date;
    }
}
