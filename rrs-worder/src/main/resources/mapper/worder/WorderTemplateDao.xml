<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.bonc.rrs.worder.dao.WorderTemplateDao">

    <resultMap id="worderTemplateDtoMap" type="com.bonc.rrs.worder.entity.dto.WorderTemplateDto">
        <result property="templateId" column="id"></result>
        <result property="templateName" column="template_name"></result>
        <result property="brandId" column="brand_id"></result>
        <result property="brandName" column="brand_name"></result>
        <result property="settleWay" column="settle_way"></result>
        <result property="autoSend" column="auto_send"></result>
        <result property="serviceTypeEnum" column="service_type_enum"></result>
        <result property="suiteId" column="suite_id"></result>
        <result property="suiteName" column="suite_name"></result>
        <result property="worderAutoCompanyBalanceId" column="company_balance_rule_id"></result>
        <result property="worderBranchBalanceId" column="dot_balance_rule_id"></result>
        <result property="addBranchBalanceId" column="dot_incre_balance_rule_id"></result>
        <result property="addUserBalanceId" column="attendant_balance_rule_id"></result>
        <result property="worderAutoCompanyBalanceName" column="worderAutoCompanyBalanceName"></result>
        <result property="worderBranchBalanceName" column="worderBranchBalanceName"></result>
        <result property="addBranchBalanceName" column="addBranchBalanceName"></result>
        <result property="addUserBalanceName" column="addUserBalanceName"></result>
        <result property="companyPledgeMoneyPercentage" column="companyPledgeMoneyPercentage"></result>
        <result property="dotPledgeMoneyPercentage" column="dotPledgeMoneyPercentage"></result>
        <!--<result property="isOneself" column="is_oneself"></result>-->
    </resultMap>

    <resultMap id="companyNamePoMap" type="com.bonc.rrs.worder.entity.po.CompanyNamePo">
        <result property="companyNo" column="company_no"></result>
        <result property="companyName" column="company_name"></result>
    </resultMap>

    <resultMap id="carTypePoMap" type="com.bonc.rrs.worder.entity.po.CarTypePo">
        <result property="carTypeId" column="car_type_id"></result>
        <result property="carTypeName" column="car_type_name"></result>
    </resultMap>

    <resultMap id="brandPoMap" type="com.bonc.rrs.worder.entity.po.BrandPo">
        <result property="brandId" column="id"></result>
        <result property="brandName" column="brand_name"></result>
    </resultMap>

    <resultMap id="regionPoMap" type="com.bonc.rrs.worder.entity.po.RegionPo">
        <result property="regionId" column="id"></result>
        <result property="regionName" column="name"></result>
    </resultMap>

    <resultMap id="worderTypePoMap" type="com.bonc.rrs.worder.entity.po.WorderTypePo">
        <result property="worderTypeId" column="id"></result>
        <result property="worderTypeName" column="name"></result>
    </resultMap>

    <resultMap id="fieldNameMap" type="com.bonc.rrs.worder.entity.po.FieldNamePo">
        <result property="fieldId" column="field_id"></result>
        <result property="fieldName" column="field_name"></result>
        <result property="fieldTypeValue" column="field_type_value"></result>
    </resultMap>

    <resultMap id="suiteNameMap" type="com.bonc.rrs.worder.entity.po.SuiteNamePo">
        <result property="suiteId" column="id"></result>
        <result property="suiteName" column="name"></result>
    </resultMap>

    <resultMap id="balanceRuleNameMap" type="com.bonc.rrs.worder.entity.po.BalanceRuleNamePo">
        <result property="balanceRuleId" column="id"></result>
        <result property="balanceRuleName" column="name"></result>
    </resultMap>

    <resultMap id="BrandEntityMap" type="com.bonc.rrs.worder.entity.po.BrandEntityPo">
        <result property="brandId" column="id"></result>
        <result property="brandName" column="brand_name"></result>
    </resultMap>

    <!--    根据结算类型和结算对象获取结算名称列表-->
    <select id="listBalanceRuleName" parameterType="com.bonc.rrs.worder.entity.query.BalanceRuleNameQuery"
        resultMap="balanceRuleNameMap">
        select a.id, a.name
        from balance_rule a
        where a.balance_type = #{balanceType}
        and a.balance_target = #{balanceTarget}
    </select>

    <select id="getCountByTemplateName" resultType="java.lang.Integer" >
        select count(0) total from worder_template
        where is_delete = '0' and template_name = #{templateName}
        <if test="templateId != null and templateId != ''">
            and id != #{templateId}
        </if>
    </select>

    <!--    获取所有套包列表-->
    <select id="listSuiteName" resultMap="suiteNameMap">
        select a.id, a.name
        from suite_information a
    </select>

    <!--    根据字段用途和字段分类获取字段名称列表-->
    <select id="listFieldNameByPurposeAndClass" parameterType="com.bonc.rrs.worder.entity.query.FieldNameQuery"
        resultMap="fieldNameMap">
        select a.field_id, a.field_name,a.field_type_value
        <if test="templateId != null and templateId != ''">
            , if(b.id, true, false) checked
        </if>
        from ext_field a
        <if test="templateId != null and templateId != ''">
            left join worder_template_field b on a.field_id = b.field_id and  b.template_id = #{templateId}
        </if>
        where a.field_purpose = #{fieldPurpose}
        and a.field_class = #{fieldClass}
        and a.deleted = '0'
        and a.is_nessary > 0
        <if test="templateId != null and templateId != ''">
            order by b.sort,a.field_id
        </if>
    </select>

    <select id="listNecessaryField" resultType="java.lang.String">
        select a.field_id
        from ext_field a
        where a.is_nessary = 0
        and a.deleted = '0'
    </select>

    <select id="listWorderType" resultMap="worderTypePoMap">
        select a.id, a.name
        from worder_type a
        where a.level = '2'
    </select>

    <!--    获取一级工单类型列表-->
    <select id="listFirstWorderType" resultMap="worderTypePoMap">
        select a.id, a.name
        from worder_type a
        where a.level = '1'
    </select>

    <!--    根据工单类型ID获取下一级工单类型-->
    <select id="listNextWorderType" resultMap="worderTypePoMap">
        select a.id, a.name
        from worder_type a
        where a.pid = #{worderTypeId}
    </select>

    <!--    获取网点品牌列表-->
    <select id="listByDotId" resultMap="brandPoMap">
        select distinct b.id,b.brand_name from sys_user su
        inner join dot_contacts dc on dc.contacts_name = su.username
        inner join dot_information di on di.dot_code = dc.dot_code
        left join dot_brand db on db.dot_id = di.dot_id
        left join brand b on db.brand_id = b.id
        where di.is_delete = 0
        <if test="userId != null and userId != ''">
            and su.user_id = #{userId}
        </if>
    </select>

    <!--    获取品牌列表-->
    <select id="listBrand" resultMap="brandPoMap">
        select distinct a.id, a.brand_name
        <if test="templateId != null and templateId != ''">
            , if(b.id, TRUE, FALSE) checked
        </if>
        from brand a
        left join manager_area_id m on a.id = m.brand_id
        left join sys_user u on m.user_id = u.user_id
        <if test="templateId != null and templateId != ''">
            left join worder_template b on a.id = b.brand_id and b.id = #{templateId}
        </if>
        <where>
            <if test="userId != null and userId != ''">
                and u.user_id = #{userId}
            </if>
        </where>
    </select>
    <!--    获取品牌列表-->
    <select id="listBrandInfo" resultMap="BrandEntityMap">
        select distinct a.id, a.brand_name
        <if test="templateId != null and templateId != ''">
            , if(b.id, TRUE, FALSE) checked
        </if>
        from brand a
        left join manager_area_id m on a.id = m.brand_id
        left join sys_user u on m.user_id = u.user_id
        <if test="templateId != null and templateId != ''">
            left join worder_template b on a.id = b.brand_id and b.id = #{templateId}
        </if>
        <where>
            <if test="userId != null and userId != ''">
                and u.user_id = #{userId}
            </if>
        </where>
    </select>


    <!--    获取品牌列表-->
    <select id="listBrands" resultMap="brandPoMap">
        select a.id, a.brand_name
        from brand a
    </select>

    <!--    获取品牌列表信息-->
    <select id="listBrandInfos" resultMap="BrandEntityMap">
        select a.id, a.brand_name
        from brand a
    </select>
    <!--    获取支持导入的品牌列表信息-->
    <select id="listImportBrandInfos" resultMap="BrandEntityMap">
        select a.id, a.brand_name
        from brand a
        where a.import_type = 1
    </select>

    <!--    根据品牌ID获取车型-->
    <select id="listCarTypeByBrandId" resultMap="carTypePoMap">
        select a.car_type_id, b.car_type_name
        from brand_car_type a
        left join car_type b on a.car_type_id = b.id
        where a.brand_id = #{brandId}
    </select>

    <!--    根据公司类型获取公司列表-->
    <select id="listCompanyNameByCompanyType" resultMap="companyNamePoMap">
        select a.company_no, a.company_name
        from company_information a
        where a.company_type = #{companyType}
    </select>

    <resultMap id="worderTemplateMap" type="com.bonc.rrs.worder.entity.po.WorderTemplatePo">
        <result property="templateId" column="id"></result>
        <result property="templateName" column="template_name"></result>
        <result property="brandId" column="brand_id"></result>
        <result property="brandName" column="brand_name"></result>
        <result property="autoSend" column="auto_send"></result>
        <result property="serviceTypeEnum" column="service_type_enum"></result>
    </resultMap>

    <!--    工单模版列表-->
    <select id="listWorderTemplate" parameterType="com.bonc.rrs.worder.entity.dto.WorderTemplateQueryDto"
            resultMap="worderTemplateMap" >
        select distinct a.id, a.template_name, a.brand_id, c.brand_name,a.gmt_create, a.auto_send, a.service_type_enum
        from worder_template a
        left join worder_template_worder_type b on a.id = b.template_id
        left join brand c on a.brand_id = c.id
        where 1=1 and   a.is_delete = '0'
            <if test="worderTypeId != null and worderTypeId != ''">
                and b.worder_type_id = #{worderTypeId}
            </if>
            <if test="brandId != null and brandId != ''">
                and a.brand_id = #{brandId}
            </if>
        order by a.gmt_create desc
        limit ${startRow}, ${limit}
    </select>

    <!--    工单模版列表-->
    <select id="listWorderTemplateCount" parameterType="com.bonc.rrs.worder.entity.dto.WorderTemplateQueryDto"
            resultType="java.lang.Integer" >

        select count(1) from (
        select distinct a.id, a.template_name, a.brand_id, c.brand_name
        from worder_template a
        left join worder_template_worder_type b on a.id = b.template_id
        left join brand c on a.brand_id = c.id
        <where>
            a.is_delete = '0'
            <if test="worderTypeId != null and worderTypeId != ''">
                and b.worder_type_id = #{worderTypeId}
            </if>
            <if test="brandId != null and brandId != ''">
                and a.brand_id = #{brandId}
            </if>
        </where>) c
    </select>

    <!--    根据工单模版ID获取工单所有信息-->
    <select id="getWorderTemplateByTemplateId" resultMap="worderTemplateDtoMap">
        select a.id, a.template_name, a.brand_id, b.brand_name
        ,a.suite_id, c.name suite_name, a.settle_way, a.attendant_balance_rule_id
        , a.company_balance_rule_id, a.dot_balance_rule_id, a.dot_incre_balance_rule_id
        ,r1.name  addUserBalanceName, r2.name worderAutoCompanyBalanceName
        ,r3.name worderBranchBalanceName, r4.name addBranchBalanceName
        ,a.company_pledge_money_percentage companyPledgeMoneyPercentage, a.dot_pledge_money_percentage dotPledgeMoneyPercentage, a.auto_send, a.service_type_enum
        from worder_template a
        left join brand b on a.brand_id = b.id
        left join suite_information c on a.suite_id = c.id
        left join balance_rule r1 on a.attendant_balance_rule_id = r1.id
        left join balance_rule r2 on a.company_balance_rule_id = r2.id
        left join balance_rule r3 on a.dot_balance_rule_id = r3.id
        left join balance_rule r4 on a.dot_incre_balance_rule_id = r4.id
        where a.id = #{templateId}
    </select>

    <!--    根据模版ID获取车型-->
    <select id="listCarTypeByTemplateId" resultMap="carTypePoMap">
        select a.id car_type_id, a.car_type_name
        from car_type  a
        left join worder_template_car b on b.car_type_id = a.id
        where b.template_id = #{templateId}
    </select>

    <!--    根据模版ID获取地区-->
    <select id="listRegionByTemplateId" resultMap="regionPoMap">
        select a.id, a.name
        from biz_region a
        left join worder_template_region b on a.id = b.region_id
        where b.template_id = #{templateId}
    </select>

    <!--    根据模版ID获取工单类型-->
    <select id="listWorderTypeByTemplateId" resultMap="worderTypePoMap">
        select a.id, a.name, c.id superTypeId, c.name superTypeName
        from worder_type a
        left join worder_template_worder_type b on a.id = b.worder_type_id
        left join worder_type c on a.pid = c.id
        where b.template_id = #{templateId}
    </select>

    <!--    根据模版ID获取字段-->
    <select id="listFieldByTemplateId" resultMap="fieldNameMap">
        select a.field_id, a.field_name
        from ext_field a
        left join worder_template_field b on a.field_id = b.field_id
        where b.template_id = #{templateId}
    </select>

    <!--    查询固定结算项信息 -->
    <select id="listWorderTemplateFixedMaterial" resultType="com.bonc.rrs.worder.entity.dto.WorderTemplateFixedMaterialDto" >
        select
            id,
            worder_status,
            template_id,
            materiel_id,
            used_num,
            field_id,
            field_value,
            category
        from
            worder_template_fixed_material
        where
            template_id = #{templateId}
    </select>

    <select id="listWorderTemplateAddedMaterialType" resultType="com.bonc.rrs.worder.entity.WorderTemplateAddedMaterialTypeEntity" >
        select
            *
        from
            worder_template_added_material_type
        where
            template_id = #{templateId}
    </select>

    <insert id="saveWorderTemplateWorderType" parameterType="java.util.List">
        insert into worder_template_worder_type (template_id, worder_type_id, gmt_create) values
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.templateId}, #{item.worderTypeId}, now())
        </foreach>
    </insert>

    <insert id="saveWorderTemplateField" parameterType="java.util.List">
        insert into worder_template_field (template_id, field_id, gmt_create, sort) values
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.templateId}, #{item.fieldId}, now(), #{item.sort})
        </foreach>
    </insert>

    <insert id="saveWorderTemplateCar" parameterType="java.util.List">
        insert into worder_template_car (template_id, car_type_id, gmt_create) values
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.templateId}, #{item.carTypeId}, now())
        </foreach>
    </insert>

    <insert id="saveWorderTemplateRegion" parameterType="java.util.List">
        insert into worder_template_region (template_id, region_id, gmt_create) values
        <foreach collection="list" item="item" separator="," close="">
            (#{item.templateId}, #{item.regionId}, now())
        </foreach>
    </insert>

    <insert id="saveWorderTemplate" parameterType="com.bonc.rrs.worder.entity.dto.WorderTemplateDto">
        <selectKey keyProperty="id" resultType="int" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into worder_template(template_name, suite_id
        , company_balance_rule_id, dot_balance_rule_id, dot_incre_balance_rule_id
        , attendant_balance_rule_id, brand_id, settle_way, gmt_create, company_pledge_money_percentage, dot_pledge_money_percentage, auto_send, service_type_enum)
        values (#{templateName}, #{suiteId}, #{worderAutoCompanyBalanceId}
        , #{worderBranchBalanceId}, #{addBranchBalanceId}, #{addUserBalanceId}
        , #{brandId}, #{settleWay}, now(), #{companyPledgeMoneyPercentage}, #{dotPledgeMoneyPercentage}, #{autoSend}, #{serviceTypeEnum})
    </insert>

    <insert id="saveWorderTemplateFixedMaterial" parameterType="java.util.List">
        insert into worder_template_fixed_material (worder_status, template_id, materiel_id, used_num, field_id, field_value,category) values
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.worderStatus}, #{item.templateId}, #{item.materielId}, #{item.usedNum}, #{item.fieldId}, #{item.fieldValue}, #{item.category})
        </foreach>
    </insert>

    <insert id="saveWorderTemplateAddedMaterialType" parameterType="java.util.List">
        insert into worder_template_added_material_type (template_id, materiel_type_id, used_num) values
        <foreach collection="list" item="item" separator="," close=";">
            (#{item.templateId}, #{item.materielTypeId}, #{item.usedNum})
        </foreach>
    </insert>

    <update id="updateWorderTemplate" parameterType="com.bonc.rrs.worder.entity.dto.WorderTemplateDto">
        update worder_template
        set template_name = #{templateName}, suite_id = #{suiteId}
        , company_balance_rule_id = #{worderAutoCompanyBalanceId}
        , dot_balance_rule_id = #{worderBranchBalanceId}
        , dot_incre_balance_rule_id = #{addBranchBalanceId}
        , attendant_balance_rule_id = #{addUserBalanceId}
        , brand_id = #{brandId}, settle_way = #{settleWay}
        , gmt_modified = now()
        , auto_send = #{autoSend}
        , service_type_enum = #{serviceTypeEnum}
        , company_pledge_money_percentage = #{companyPledgeMoneyPercentage}, dot_pledge_money_percentage = #{dotPledgeMoneyPercentage}
        where id = #{templateId}
    </update>

    <update id="updateTemplateRegion" parameterType="com.bonc.rrs.worder.entity.dto.WorderTemplateRegionDto">
        update worder_template_region
        set region_id = #{regionId}, gmt_modified = now()
        where template_id = #{templateId}
    </update>

    <update id="updateWorderTemplateWorderType" parameterType="com.bonc.rrs.worder.entity.dto.WorderTemplateWorderTypeDto">
        update worder_template_worder_type
        set worder_type_id = #{worderTypeId}, gmt_modified = now()
        where template_id = #{templateId}
    </update>

    <update id="updateWorderTemplateField" parameterType="com.bonc.rrs.worder.entity.dto.WorderTemplateFieldDto">
        update worder_template_field
        set field_id = #{fieldId}, gmt_modified = now()
        where template_id = #{templateId}
    </update>

    <update id="updateWorderTemplateCar" parameterType="com.bonc.rrs.worder.entity.dto.WorderTemplateCarDto">
        update worder_template_car
        set car_type_id = #{carTypeId}, gmt_modified = now()
        where template_id = #{templateId}
    </update>

    <delete id="deleteTemplateRegion" parameterType="java.lang.String">
        delete from worder_template_region
        where template_id = #{templateId}
    </delete>

    <delete id="deleteWorderTemplateWorderType" parameterType="java.lang.String">
        delete from worder_template_worder_type
        where template_id = #{templateId}
    </delete>

    <delete id="deleteWorderTemplateField" parameterType="java.lang.String">
        delete from worder_template_field
        where template_id = #{templateId}
    </delete>

    <delete id="deleteWorderTemplateCar" parameterType="java.lang.String">
        delete from worder_template_car
        where template_id = #{templateId}
    </delete>

    <delete id="deleteWorderTemplateFixedMaterial" parameterType="java.lang.Integer">
        delete from worder_template_fixed_material
        where template_id = #{templateId}
    </delete>

    <delete id="deleteWorderTemplateAddedMaterialType" parameterType="java.lang.Integer">
        delete from worder_template_added_material_type
        where template_id = #{templateId}
    </delete>

    <!--    删除工单模版-->
    <update id="deleteWorderTemplate">
        update worder_template
        set is_delete = '1'
        where id = #{templateId}
    </update>
    
    <select id="findTemplateInfoById" resultMap="worderTemplateDtoMap">
        select t.* from worder_template t where  t.id=#{id}
    </select>
    <update id="updateByWorderTempId">
        update worder_template
        set is_oneself = #{isOneself}
        where id = #{templateId}
    </update>
    <select id="getListByWorderIds" resultType="com.bonc.rrs.worder.entity.dto.WorderTemplateDto">
        select wt.*,wi.worder_id
        from worder_information wi , worder_template wt
        where wi.template_id = wt.id
        and wi.worder_id in
        <foreach collection="worderIds" item="worderId" open="(" close=")" separator=",">
            #{worderId}
        </foreach>
    </select>

    <select id="findTemplateInfoByBrandIdAndWorderTypeIdAndRegion" resultMap="worderTemplateDtoMap">
        select
            wt.*
        from
            worder_template wt
        inner join worder_template_worder_type wtwt on
            wt.id = wtwt.template_id
        inner join worder_template_region wtr on
            wtr.template_id = wt.id
        where
            wt.brand_id = #{brandId}
            and wtwt.worder_type_id = #{worderTypeId}
            and wtr.region_id = #{regionId}
            and wt.is_delete = 0
        order by
            wt.gmt_create
    </select>

</mapper>