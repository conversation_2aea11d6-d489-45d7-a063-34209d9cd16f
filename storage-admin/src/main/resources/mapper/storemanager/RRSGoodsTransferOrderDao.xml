<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="bonc.storage.modules.storemanager.dao.RRSGoodsTransferOrderDao">
    <resultMap id="transferOrderEntity" type="bonc.storage.modules.storemanager.entity.RRSGoodsTransferOrderEntity">
        <id property="id" column="id"/>
        <result property="applyCode" column="apply_code"/>
        <result property="applyTime" column="apply_time"/>
        <result property="srcStoreId" column="scr_store_id"/>
        <result property="storeId" column="store_id"/>
        <result property="applyStatus" column="apply_status"/>
        <result property="applyMode" column="apply_mode"/>
        <result property="applyMemo" column="apply_memo"/>
        <result property="creatTime" column="creat_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="operateUser" column="operate_user"/>
        <result property="expressName" column="express_name"/>
        <result property="expressNum" column="express_num"/>
    </resultMap>

    <resultMap id="goodsLeaveEntity" type="bonc.storage.modules.storemanager.entity.RRSGoodsLeaveOrderEntity">
        <id property="id" column="id"/>
        <result property="leaveNum" column="leave_num"/>
        <result property="leaveDate" column="leave_date"/>
        <result property="storeId" column="store_id"/>
        <result property="leaveType" column="leave_type"/>
        <result property="goStatus" column="go_status"/>
        <result property="operateUser" column="operate_user"/>
        <result property="creatTime" column="creat_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="num" column="num"/>
    </resultMap>

    <resultMap id="leaveRelationEntity" type="bonc.storage.modules.storemanager.entity.RRSGoodsLeaveOrderEntity">
        <id property="id" column="id"/>
        <result property="goodsId" column="goods_id"/>
        <result property="creatTime" column="creat_time"/>
        <result property="leaveId" column="leave_id"/>
        <result property="fromGoodsName" column="from_goodsName"/>
        <result property="leaveNum" column="leave_num"/>
        <result property="installNum" column="install_num"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="district" column="district"/>
        <result property="installAddr" column="install_addr"/>
        <result property="expressName" column="express_name"/>
        <result property="expressNum" column="express_num"/>
    </resultMap>

    <select id="RRSGoodsOutOrderList" resultType="java.util.HashMap" parameterType="java.util.Map">
        select sai.apply_code applyCode,
        sai.id id,
        sai.apply_time applyTime,
        sai.src_store_id srcStoreId,
        sai.store_id storeId,
        sai.apply_mode applyMode,
        <!--sai.apply_status applyStatus,-->
        sbi2.store_name storeNameTwo,
        SUM(sag.actual_num) actualNum,
        sbi.store_name storeNameOne,
        (CASE WHEN sai.apply_status = 0 THEN '待审核'
        WHEN sai.apply_status = 1 THEN '审核通过'
        WHEN sai.apply_status = 2 THEN '已拒绝' END)applyStatus
        from rrs_shop_apply_info sai
        left join rrs_store_basic_info sbi on sai.src_store_id=sbi.id
        left join rrs_store_basic_info sbi2 on sai.store_id=sbi2.id
        LEFT JOIN rrs_shop_apply_goods sag ON sag.apply_id = sai.id
        where 1=1
        <if test="userId!=null and userId!=''">
            and sai.user_id in (${userId})
        </if>
        <!--  and user_id=#{userId} -->
        <if test="applyCode!=null and applyCode!=''">
            and sai.apply_code=#{applyCode}
        </if>
        <if test="srcStoreId!=null and srcStoreId!=''">
            and sai.src_store_id=#{srcStoreId}
        </if>
        <if test="storeId!=null and storeId!=''">
            and sai.store_id=#{storeId}
        </if>
        <if test="beginTime!=null and beginTime!=''">
            and sai.apply_time <![CDATA[ >= ]]> #{beginTime}
        </if>
        <if test="endTime!=null and endTime!=''">
            and sai.apply_time <![CDATA[ <= ]]> #{endTime}
        </if>
        GROUP BY sai.apply_code
        order by sai.apply_time desc
        limit #{offset},#{limit}
    </select>

    <select id="RRSGoodsOutOrdertotalCount" resultType="Integer" parameterType="java.util.Map">
        select count(*) from (
        select sai.apply_code from rrs_shop_apply_info sai
        left join rrs_store_basic_info sbi on sai.src_store_id=sbi.id
        left join rrs_store_basic_info sbi2 on sai.store_id=sbi2.id
        left join rrs_shop_apply_goods sag ON sag.apply_id = sai.id
        where 1=1
        <if test="userId!=null and userId!=''">
            and sai.user_id in (${userId})
        </if>
        <if test="applyCode!=null and applyCode!=''">
            and sai.apply_code=#{applyCode}
        </if>
        <if test="srcStoreId!=null and srcStoreId!=''">
            and sai.src_store_id=#{srcStoreId}
        </if>
        <if test="storeId!=null and storeId!=''">
            and sai.store_id=#{storeId}
        </if>
        <if test="beginTime!=null and beginTime!=''">
            and sai.apply_time <![CDATA[ >= ]]> #{beginTime}
        </if>
        <if test="endTime!=null and endTime!=''">
            and sai.apply_time <![CDATA[ <= ]]> #{endTime}
        </if>
        group by
        sai.apply_code) aggr
    </select>

    <select id="queryStoreByUser" resultType="map" parameterType="java.lang.Integer">
        select id, store_name as storeName
        from rrs_store_basic_info sbi
                 left join rrs_user_store_relation usr on sbi.id = usr.store_id
        where usr.user_id = #{userId}
    </select>

    <select id="queryAllStore" resultType="map" parameterType="java.lang.Integer">
        select id, store_name as storeName
        from rrs_store_basic_info sbi
    </select>

    <select id="queryGoodsByStore" resultType="java.util.HashMap" parameterType="java.lang.Integer">
        select ssg.id,
        ssg.goods_id goodsId,
        ssg.goods_type goodsType,
        ssg.goods_total as total,
        ssg.store_position_id as storePositionId,
        sp.position_name as positionName,
        mi.materiel_name as materielName,
        mi.materiel_no as materielNo,
        mi.materiel_unit_value as materielUnitValue,
        mi.materiel_brand_value as materielBrandValue,
        mi.materiel_management_type materielManagementType
        from rrs_store_stock_goods ssg
        left join materiel_information mi on ssg.goods_id=mi.id
        left join rrs_store_position_info sp on sp.id = ssg.store_position_id
        where ssg.store_id=#{storeId}
        <if test="materielName !=null and materielName!=''">
            and mi.materiel_name LIKE CONCAT('%',#{materielName},'%')
        </if>
        <if test="storePositionId != null and storePositionId != '' ">
            and ssg.store_position_id = #{storePositionId}
        </if>
        <if test="goodsType != null and goodsType != ''">
            and ssg.goods_type = #{goodsType}
        </if>
        order by ssg.latest_in_time desc
        limit #{offset},#{limit}
    </select>


    <insert id="addGoodsTransferOrder" parameterType="bonc.storage.modules.storemanager.dto.GoodsTransferOrderDTO"
            useGeneratedKeys="true" keyProperty="id">
        insert into rrs_shop_apply_info (apply_code,
                                         apply_time,
                                         src_store_id,
                                         store_id,
                                         apply_status,
                                         apply_mode,
                                         apply_memo,
                                         creat_time,
                                         operate_user,
                                         user_id,
                                         express_num,
                                         express_name)
        values (#{applyCode},
                now(),
                #{srcStoreId},
                #{storeId},
                #{applyStatus},
                #{applyMode},
                #{applyMemo},
                now(),
                #{operateUser},
                #{userId},
                #{expressNum},
                #{expressName})
    </insert>

    <insert id="addTransferGoods" parameterType="java.util.Map">
        insert into rrs_shop_apply_goods (apply_id,
                                          goods_id,
                                          goods_name,
                                          goods_unit,
                                          goods_num,
                                          actual_num,
                                          creat_time)
        values (#{applyId},
                #{goodsId},
                #{goodsName},
                #{goodsUnit},
                #{goodsNum},
                #{actualNum},
                now())
    </insert>
    <update id="updateStock" parameterType="java.util.Map">
        update rrs_store_stock_goods
        set goods_num=goods_num - #{goodsNum}
        where goods_id = #{goodsId}
          and store_id = #{storeId}
    </update>
    <insert id="addGoodsLeaveOrder" parameterType="bonc.storage.modules.storemanager.entity.RRSGoodsLeaveOrderEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into rrs_goods_leave_order(leave_num,
                                          leave_date,
                                          store_id,
                                          leave_type,
                                          go_status,
                                          operate_user,
                                          creat_time)
        values (#{leaveNum},
                now(),
                #{storeId},
                #{leaveType},
                #{goStatus},
                #{operateUser},
                now())
    </insert>
    <insert id="addGoodsLeaveRelation"
            parameterType="bonc.storage.modules.storemanager.entity.RRSGoodsLeaveRelationEntity"
            useGeneratedKeys="true" keyProperty="id">
        insert into rrs_goods_leave_relation(goods_id,
                                             creat_time,
                                             leave_id,
                                             from_goodsName,
                                             leave_num,
                                             install_num,
                                             province,
                                             city,
                                             district,
                                             install_addr,
                                             express_name,
                                             express_num)
        values (#{goodsId},
                now(),
                #{leaveId},
                #{fromGoodsName},
                #{leaveNum},
                #{installNum},
                #{province},
                #{city},
                #{district},
                #{installAddr},
                #{expressName},
                #{expressNum})
    </insert>
    <select id="queryApplyCode" resultType="java.lang.String" parameterType="java.lang.String">
        select apply_code
        from rrs_shop_apply_info
        where apply_code like #{param}
        order by apply_code desc limit 1
    </select>
    <select id="queryTransfer" resultType="map" parameterType="java.util.Map">
    </select>

    <insert id="insertAuditRecord">
        INSERT
        rrs_audit_record
        (apply_code,
        receive_user_id,
        audit_status,
        create_time)
        VALUES
        (
        #{applyCode},
        #{operateUser},
        #{applyStatus},
        #{creatTime}
        )
    </insert>

    <insert id="insertAuditRecord2">
        INSERT
            rrs_audit_record
        (apply_code,
         receive_user_id,
         audit_status,
         audit_user_id,
         no_pass_reason,
         create_time,
         audit_time)
        VALUES
            (
                #{applyCode},
                #{operateUser},
                #{auditStatus},
                #{auditUserId},
                #{noPassReason},
                now(),
                now()
            )
    </insert>

    <select id="queryTransferGoodsInfo" resultType="bonc.storage.modules.storemanager.entity.RRSTransferGoodsInfoEntity" parameterType="integer">
        select
            rsag.*,
            mi.materiel_management_type,
            ssg.goods_total
        from
            rrs_shop_apply_goods rsag
        left join materiel_information mi on
            rsag.goods_id = mi.id
        left join rrs_store_stock_goods ssg on
            rsag.goods_id = ssg.goods_id and rsag.out_position_id = ssg.store_position_id and rsag.goods_type = ssg.goods_type
        where
            rsag.apply_id = #{applyId}
    </select>

    <update id="updateAudit">
        UPDATE
            rrs_shop_apply_info
        SET apply_status = #{auditStatus}
        WHERE apply_code = #{applyCode}
    </update>

    <update id="updateActualNumById">
        UPDATE
            rrs_shop_apply_goods
        SET actual_num = #{actualNum}
        WHERE id = #{id}
    </update>

    <update id="UpdateAuditRecord">
     update rrs_audit_record set audit_status=#{auditStatus},
                                audit_user_id=#{auditUserId},
                                no_pass_reason=#{noPassReason},
                                audit_time = now()
        where apply_code=#{applyCode}
    </update>

    <select id="getAuditStatusOne" resultType="java.util.Map">
        SELECT id,
               apply_code      applyCode,
               receive_user_id receiveUserId,
               audit_user_id   auditUserId,
               audit_status    auditStatus,
               no_pass_reason  noPassReason,
               create_time     createTime,
               audit_time      auditTime
        FROM rrs_audit_record
        WHERE apply_code = #{applyCode}
          AND audit_status = #{auditStatus}
        ORDER BY create_time DESC LIMIT 0,1
    </select>

    <select id="getListById" resultType="java.util.Map">
        select rsag.id                 id,
               rsag.apply_id           applyId,
               rsag.goods_id           goodsId,
               rsag.goods_name         materielName,
               rsag.goods_unit         goodsUnit,
               rsag.goods_num          goodsNum,
               rsag.actual_num         actualNum,
               mi.materiel_no          materielNo,
               mi.materiel_brand_value materielBrandValue,
               rsag.out_position_id    outPositionId,
               (select position_name from rrs_store_position_info where id = rsag.out_position_id)      outPositionName,
               rsag.in_position_id     inPositionId,
               (select position_name from rrs_store_position_info where id = rsag.in_position_id)      inPositionName,
               rsag.goods_type         goodsType,
               rssg.goods_total        total
        from rrs_shop_apply_goods rsag
                 left join rrs_shop_apply_info rsai on
            rsai.id = rsag.apply_id
                 left join materiel_information mi on
            mi.id = rsag.goods_id
                 left join rrs_store_stock_goods rssg on
            rsag.goods_id = rssg.goods_id
                and rsai.src_store_id = rssg.store_id
                and rsag.goods_type = rssg.goods_type
                and rsag.out_position_id = rssg.store_position_id
        where rsag.apply_id = #{id}
    </select>

    <select id="queryGoodsByStoreCount" resultType="java.lang.Integer" parameterType="java.util.Map">
        select count(*)
        from rrs_store_stock_goods ssg
        left join materiel_information mi on ssg.goods_id=mi.id
        left join rrs_store_position_info sp on sp.id = ssg.store_position_id
        where ssg.store_id=#{storeId}
        <if test="materielName!=null and materielName!=''">
            and mi.materiel_name LIKE CONCAT('%',#{materielName},'%')
        </if>
        <if test="storePositionId !=null and storePositionId != ''">
            and ssg.store_position_id = #{storePositionId}
        </if>
        <if test="goodsType!=null and goodsType!=''">
            and ssg.goods_type = #{goodsType}
        </if>
    </select>

    <select id="selectByOutCode" resultMap="transferOrderEntity">
        select
            rsai.*
        from
            rrs_enter_leave_info reli ,
            rrs_shop_apply_info rsai
        where
            reli.out_code = rsai.apply_code
          and reli.enter_code = #{outCode}
          and reli.mode = 1
          and reli.status = 1
    </select>
</mapper>
