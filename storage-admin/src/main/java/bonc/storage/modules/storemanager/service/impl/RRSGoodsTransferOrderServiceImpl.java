package bonc.storage.modules.storemanager.service.impl;

import bonc.storage.core.utils.PageUtils;
import bonc.storage.core.utils.Query;
import bonc.storage.core.utils.Result;
import bonc.storage.modules.storemanagent.dao.RrsStoreBasicInfoMapper;
import bonc.storage.modules.storemanagent.entity.RrsStoreBasicInfo;
import bonc.storage.modules.storemanager.constant.ConstantPool;
import bonc.storage.modules.storemanager.dao.*;
import bonc.storage.modules.storemanager.dto.GoodsTransferOrderDTO;
import bonc.storage.modules.storemanager.dto.GoodsTransferOrderMappper;
import bonc.storage.modules.storemanager.entity.*;
import bonc.storage.modules.storemanager.service.IRRSGoodsEnterOrderService;
import bonc.storage.modules.storemanager.service.IRRSGoodsTransferOrderService;
import bonc.storage.modules.storemanager.util.UserRoleFilter;
import bonc.storage.modules.storemanager.vo.GoodsTransferOrderFromVO;
import bonc.storage.modules.sys.service.SystemTokenService;
import bonc.storage.modules.systemmanagement.entity.UserEntity;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import com.youngking.renrenwithactiviti.modules.sys.service.SysRoleService;
import lombok.AllArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Log4j2
public class RRSGoodsTransferOrderServiceImpl implements IRRSGoodsTransferOrderService {
    @Autowired(required = false)
    RRSGoodsTransferOrderDao rrsGoodsTransferOrderDao;
    @Autowired(required = false)
    GoodsTransferOrderMappper goodsTransferOrderMappper;

    @Autowired(required = false)
    RRSGoodsTransferDao rrsGoodsTransferDao;
    @Autowired(required = false)
    SystemTokenService systemTokenService;
    @Autowired(required = false)
    private SysRoleService sysRoleService;

    @Autowired(required = false)
    private RRSGoodsEnterOrderDao rrsGoodsEnterOrderDao;

    private final IRRSGoodsEnterOrderService irrsGoodsEnterOrderService;

    private final RRSGoodsLeaveOrderDao rrsGoodsLeaveOrderDao;

    private final RRSGoodsLeaveRelationDao rrsGoodsLeaveRelationDao;

    private final RrsStoreBasicInfoMapper storeBasicInfoMapper;

    private final UserRoleFilter userRoleFilter;

    @Override
    public PageUtils RRSGoodsTransferOrderList(Query query) {
        //获取用户角色信息
        List<String> listIds = userRoleFilter.filterRole(getUser());
        if (Objects.nonNull(listIds)) {
            query.put("userId", String.join(",", listIds));
        }
        List<Map> list = rrsGoodsTransferOrderDao.RRSGoodsOutOrderList(query);
        //添加出库或者入库单号
        list.forEach(map -> {
            //根据调拨单查询关联的出库单和入库单
            List<RRSEnterLeaveInfoEntity> enterLeaveInfoList = rrsGoodsLeaveOrderDao.selectInAndOutOrderByApplyCode(map.get("applyCode").toString());
            if (Objects.nonNull(enterLeaveInfoList) && !enterLeaveInfoList.isEmpty()) {
                //获取入库单记录
                List<RRSEnterLeaveInfoEntity> rrsInEnterLeaveInfoEntity = enterLeaveInfoList.stream().filter(rrsEnterLeaveInfoEntity -> ConstantPool.IN_LIBRARY.equals(rrsEnterLeaveInfoEntity.getMode())).collect(Collectors.toList());
                if (rrsInEnterLeaveInfoEntity.size() > 0) {
                    String name = ConstantPool.InAndOutOrderStatus.getName(rrsInEnterLeaveInfoEntity.get(0).getStatus());
                    if (org.apache.commons.lang.StringUtils.isNotBlank(name)) {
                        name = "(" + name + ")";
                    }

                    map.put("inCode", rrsInEnterLeaveInfoEntity.get(0).getEnterCode() + name);
                }

                //获取出库单记录
                List<RRSEnterLeaveInfoEntity> rrsOutEnterLeaveInfoEntity = enterLeaveInfoList.stream().filter(rrsEnterLeaveInfoEntity -> ConstantPool.OUT_LIBRARY.equals(rrsEnterLeaveInfoEntity.getMode())).collect(Collectors.toList());
                if (rrsOutEnterLeaveInfoEntity.size() > 0) {
                    String name = ConstantPool.InAndOutOrderStatus.getName(rrsOutEnterLeaveInfoEntity.get(0).getStatus());
                    if (org.apache.commons.lang.StringUtils.isNotBlank(name)) {
                        name = "(" + name + ")";
                    }
                    map.put("outCode", rrsOutEnterLeaveInfoEntity.get(0).getEnterCode() + name);
                }
            }
        });

        return new PageUtils(list, rrsGoodsTransferOrderDao.RRSGoodsOutOrdertotalCount(query), query.getLimit(), query.getPage());
    }

    @Override
    public List queryStoreByUser(Integer userId) {
        return rrsGoodsTransferOrderDao.queryStoreByUser(userId);
    }

    @Override
    public List queryAllStore(Integer userId) {
        return rrsGoodsTransferOrderDao.queryAllStore(userId);
    }

    @Override
    public PageUtils queryGoodsByStore(JSONObject params) {
        Query query = new Query(params);
        List<Map<String, Object>> list = rrsGoodsTransferOrderDao.queryGoodsByStore(query);
        return new PageUtils(list, rrsGoodsTransferOrderDao.queryGoodsByStoreCount(query), query.getLimit(), query.getPage());
    }


    @Override
    @Transactional
    public void addGoodsTransferOrder(GoodsTransferOrderFromVO goodsTransferOrderFromVO, UserEntity user) {
        GoodsTransferOrderDTO goodsTransferOrderDTO = goodsTransferOrderMappper.GoodsTransferOrder2DTO(goodsTransferOrderFromVO);
        goodsTransferOrderDTO.setOperateUser(user.getName());
        goodsTransferOrderDTO.setUserId(user.getId().toString());
        goodsTransferOrderDTO.setApplyStatus("1");//1未待确认，2为确认，3为已拒绝
        //新增调拨单
        rrsGoodsTransferOrderDao.addGoodsTransferOrder(goodsTransferOrderDTO);
        //新增一个物料出库单
        RRSGoodsLeaveOrderEntity leaveOrderEntity = new RRSGoodsLeaveOrderEntity();
        leaveOrderEntity.setLeaveType(1);
        leaveOrderEntity.setGoStatus("0");
        leaveOrderEntity.setOperateUser(user.getName());
        leaveOrderEntity.setStoreId(goodsTransferOrderDTO.getSrcStoreId());
        rrsGoodsTransferOrderDao.addGoodsLeaveOrder(leaveOrderEntity);
        RRSGoodsLeaveRelationEntity goodsLeaveRelationEntity = new RRSGoodsLeaveRelationEntity();
        //按商品新增正品出库关系
        //循环新增调拨单商品和扣除库存
        goodsTransferOrderFromVO.getTransferGoods().forEach(rrstransferGoodsInfoEntity -> {
            goodsLeaveRelationEntity.setLeaveId(leaveOrderEntity.getId());
            goodsLeaveRelationEntity.setGoodsId(rrstransferGoodsInfoEntity.getGoodsId());
            goodsLeaveRelationEntity.setLeaveNum(rrstransferGoodsInfoEntity.getGoodsNum());
//            goodsLeaveRelationEntity.setFromGoodsName(rrstransferGoodsInfoEntity.getGoodsName());
            rrsGoodsTransferOrderDao.addGoodsLeaveRelation(goodsLeaveRelationEntity);
            rrstransferGoodsInfoEntity.setApplyId(goodsTransferOrderDTO.getId());
            rrsGoodsTransferOrderDao.addTransferGoods(rrstransferGoodsInfoEntity);
//            rrsGoodsTransferOrderDao.updateStock(rrstransferGoodsInfoEntity.getGoodsId(),rrstransferGoodsInfoEntity.getGoodsNum(),goodsTransferOrderDTO.getSrcStoreId());
        });
    }

    @Override
    public String queryApplyCode() {
        SimpleDateFormat f = new SimpleDateFormat("yyyyMMdd");//设置日期格式
        String date = f.format(new Date(System.currentTimeMillis()));
        String applyCode = rrsGoodsTransferOrderDao.queryApplyCode("db-" + date + "-");
        if (applyCode != null && applyCode != "") {
            Integer intNumber = Integer.parseInt(applyCode.substring(12)) + 1;
            String number = String.valueOf(intNumber);
            for (int i = 0; i < 6; i++) {
                number = number.length() < 6 ? "0" + number : number;
            }
            return "db-" + date + "-" + number;
        } else {
            return "db-" + date + "-000001";
        }
    }

    @Override
    public String queryLeaveNum() {
        SimpleDateFormat f = new SimpleDateFormat("yyyyMMdd");//设置日期格式
        String date = f.format(new Date(System.currentTimeMillis()));
        String applyCode = rrsGoodsTransferOrderDao.queryLeaveNum("ck-" + date + "-");
        if (applyCode != null || applyCode != "") {
            Integer intNumber = Integer.parseInt(applyCode.substring(12));
            String number = String.valueOf(intNumber);
            for (int i = 0; i < 6; i++) {
                number = number.length() < 6 ? "0" + number : number;
            }
            return "ck-" + date + "-" + number;
        } else {
            return "ck-" + date + "-000001";
        }
    }

    @Override
    public PageUtils queryTransfer(Query query) {
        List<Map> list = rrsGoodsTransferOrderDao.queryTransfer(query);
        return new PageUtils(list, rrsGoodsTransferOrderDao.queryTransferCount(query), query.getLimit(), query.getPage());
    }


    @Override
    public void addTransferAndGoodsInfo(GoodsTransferOrderFromVO goodsTransferOrderFromVO) {
        //现将调拨的信息保存
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = sdf.parse(sdf.format(new Date(System.currentTimeMillis())));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        RRSGoodsTransferOrderEntity transferEntity = new RRSGoodsTransferOrderEntity();

//        StringBuilder addr = new StringBuilder();
//        String address = goodsTransferOrderFromVO.getAddress();
//        //翻译省市县编码为中文
//        if (org.apache.commons.lang.StringUtils.isNotBlank(address)) {
//            String[] split = address.split(",");
//            addr.append(rrsGoodsEnterOrderDao.getBizRegionById(Integer.parseInt(split[0]))).append(",");
//            addr.append(rrsGoodsEnterOrderDao.getBizRegionById(Integer.parseInt(split[1]))).append(",");
//            addr.append(split[2]);
//            transferEntity.setAddress(addr.toString());
//        }
        transferEntity.setAddress(goodsTransferOrderFromVO.getAddress());
        transferEntity.setPhone(goodsTransferOrderFromVO.getPhone());
        transferEntity.setCompanyNumber(goodsTransferOrderFromVO.getCompanyNumber());
        transferEntity.setFromName(goodsTransferOrderFromVO.getFromName());
        transferEntity.setApplyTime(date);
        transferEntity.setApplyCode(goodsTransferOrderFromVO.getApplyCode());
        transferEntity.setSrcStoreId(goodsTransferOrderFromVO.getSrcStoreId());
        transferEntity.setStoreId(goodsTransferOrderFromVO.getStoreId());
        transferEntity.setApplyMemo(goodsTransferOrderFromVO.getApplyMemo());
        transferEntity.setExpressNum(goodsTransferOrderFromVO.getExpressNum());
        transferEntity.setApplyStatus("0");
        transferEntity.setOperateUser(getUser().getUsername());
        transferEntity.setUserId(getUser().getUserId().toString());

        if (StringUtils.isEmpty(goodsTransferOrderFromVO.getId())) {
            transferEntity.setCreatTime(new Date());
            rrsGoodsTransferOrderDao.insert(transferEntity);
            rrsGoodsTransferOrderDao.insertAuditRecord(transferEntity);
            //将物料保存
            List<RRSTransferGoodsInfoEntity> list = goodsTransferOrderFromVO.getTransferGoods();
            for (RRSTransferGoodsInfoEntity transferGoods : list) {
                transferGoods.setApplyId(transferEntity.getId());
                transferGoods.setCreatTime(new Date());
                rrsGoodsTransferDao.insert(transferGoods);
            }
        } else {
            transferEntity.setUpdateTime(new Date());
            transferEntity.setId(goodsTransferOrderFromVO.getId());
            rrsGoodsTransferOrderDao.updateById(transferEntity);

            List<RRSTransferGoodsInfoEntity> list = goodsTransferOrderFromVO.getTransferGoods();
            for (RRSTransferGoodsInfoEntity transferGoods : list) {
                transferGoods.setApplyId(transferEntity.getId());
                transferGoods.setUpdateTime(new Date());
                rrsGoodsTransferDao.updateById(transferGoods);
            }
        }
    }


    @Override
    public Map<String, Object> queryTransferInfoById(Integer id) {
        //处理地址返回回编码
        List<RegionCodeVo> listRegions = rrsGoodsEnterOrderDao.selectReginCode();
        //先查订单的信息
        RRSGoodsTransferOrderEntity transferOrderEntity = rrsGoodsTransferOrderDao.selectById(id);
        //获取省市信息
        String address = transferOrderEntity.getAddress();
        if (org.apache.commons.lang.StringUtils.isNotBlank(address)) {
            StringBuilder addr = new StringBuilder();
            String[] addressArr = address.split(",");
            Optional<RegionCodeVo> province = listRegions.stream()
                    .filter(regionCodeVo -> "1".equals(regionCodeVo.getType()) && addressArr[0].equals(regionCodeVo.getName()))
                    .findFirst();
            if (province.isPresent()) {
                addr.append(province.get().getId()).append(",");
            } else {
                addr.append(addressArr[0]).append(",");
            }
            //addr.append(listRegions.stream().filter(regionCodeVo -> "1".equals(regionCodeVo.getType()) && addressArr[0].equals(regionCodeVo.getName())).findFirst().get().getId()).append(",");
            //addr.append(listRegions.stream().filter(regionCodeVo -> "2".equals(regionCodeVo.getType()) && addressArr[1].equals(regionCodeVo.getName())).findFirst().get().getId()).append(",");
            Optional<RegionCodeVo> city = listRegions.stream()
                    .filter(regionCodeVo -> "2".equals(regionCodeVo.getType()) && addressArr[1].equals(regionCodeVo.getName()))
                    .findFirst();
            if (city.isPresent()) {
                addr.append(city.get().getId()).append(",");
            } else {
                addr.append(addressArr[1]).append(",");
            }
            addr.append(addressArr[2]);
            transferOrderEntity.setAddress(addr.toString());
        }

        //在查物料信息
        QueryWrapper<RRSTransferGoodsInfoEntity> wrapper = new QueryWrapper();
        wrapper.eq("apply_id", id);
//        List<RRSTransferGoodsInfoEntity> list = rrsGoodsTransferDao.selectList(wrapper);
        List<Map<String, Object>> list = rrsGoodsTransferOrderDao.getListById(id);
        Map<String, Object> map = new HashMap<>();
        map.put("transferOrderEntity", transferOrderEntity);
        map.put("list", list);

        return map;
    }

    @Override
    public Result updateAudit(JSONObject param) {

        if (param.getInteger("status") == 1) {

            JSONArray actualNums = param.getJSONArray("actualNums");
            actualNums.forEach(obj -> {
                JSONObject actualNumObj = (JSONObject) obj;
                rrsGoodsTransferOrderDao.updateActualNumById(actualNumObj.getInteger("actualNum"), actualNumObj.getInteger("id"));
            });

            rrsGoodsTransferOrderDao.updateAudit(param.getString("applyCode"), param.getInteger("status"));
            rrsGoodsTransferOrderDao.UpdateAuditRecord(param.getString("applyCode"), param.getInteger("status"), getUser().getUsername(), "审核通过");
            //生成出库单
            generateOutboundOrder(param.getString("applyCode"));


        } else if (param.getInteger("status") == 2) {
            rrsGoodsTransferOrderDao.updateAudit(param.getString("applyCode"), param.getInteger("status"));
            rrsGoodsTransferOrderDao.UpdateAuditRecord(param.getString("applyCode"), param.getInteger("status"), getUser().getUsername(),"审核不通过：" + param.getString("noPassReason"));
        } else if (param.getInteger("status") == 0) {
            rrsGoodsTransferOrderDao.updateAudit(param.getString("applyCode"), param.getInteger("status"));
            rrsGoodsTransferOrderDao.UpdateAuditRecord(param.getString("applyCode"), param.getInteger("status"), getUser().getUsername(), "");
        }
        return Result.ok();
    }

    /**
     * 调拨审核完成生成出库单
     *
     * @param applyCode
     */
    void generateOutboundOrder(String applyCode) {
        //根据调拨单查询调拨申请单表
        QueryWrapper<RRSGoodsTransferOrderEntity> transferOrderEntityQueryWrapper = new QueryWrapper<>();
        transferOrderEntityQueryWrapper.eq("apply_code", applyCode);
        RRSGoodsTransferOrderEntity transferOrderEntity = rrsGoodsTransferOrderDao.selectOne(transferOrderEntityQueryWrapper);
        //获取出库单号
        String leaveCodeAndTime = irrsGoodsEnterOrderService.getLeaveCodeAndTime();
        //获取调拨的物料信息
//        QueryWrapper<RRSTransferGoodsInfoEntity> transferGoodsInfoEntityQueryWrapper = new QueryWrapper<>();
//        transferGoodsInfoEntityQueryWrapper.eq("apply_id", transferOrderEntity.getId());
//        List<RRSTransferGoodsInfoEntity> transferGoodsInfos = rrsGoodsTransferDao.selectList(transferGoodsInfoEntityQueryWrapper);

        List<RRSTransferGoodsInfoEntity> transferGoodsInfos = rrsGoodsTransferOrderDao.queryTransferGoodsInfo(transferOrderEntity.getId());

        //查询出库方库仓库信息
        RrsStoreBasicInfo rrsStoreBasicInfo = storeBasicInfoMapper.selectById(transferOrderEntity.getSrcStoreId());

        //物料出库单
        RRSGoodsLeaveOrderEntity orderEntity = new RRSGoodsLeaveOrderEntity();
        orderEntity.setLeaveNum(leaveCodeAndTime);
        orderEntity.setLeaveDate(new Date());
        orderEntity.setStoreId(transferOrderEntity.getSrcStoreId());
        orderEntity.setGoStatus("0");
        orderEntity.setOperateUser(getUser().getUsername());
        orderEntity.setLeaveMemo("调拨生成的出库单");
        orderEntity.setCreatTime(new Date());
        orderEntity.setOutCode(applyCode);
        orderEntity.setNum(transferGoodsInfos.stream().mapToInt(RRSTransferGoodsInfoEntity::getActualNum).sum());
        orderEntity.setOutScenario("2");
        rrsGoodsLeaveOrderDao.insert(orderEntity);
        //出入库明细表
        RRSEnterLeaveInfoEntity enterLeaveInfo = new RRSEnterLeaveInfoEntity();
        enterLeaveInfo.setEnterCode(leaveCodeAndTime);
        enterLeaveInfo.setCreateTime(new Date());
        enterLeaveInfo.setStoreId(transferOrderEntity.getSrcStoreId());
        enterLeaveInfo.setMode("1");
        enterLeaveInfo.setStatus("0");
        enterLeaveInfo.setExpressNum(transferOrderEntity.getExpressNum());

        enterLeaveInfo.setFromName(rrsStoreBasicInfo.getMangerUser());

        enterLeaveInfo.setPhone(rrsStoreBasicInfo.getConTel());

        enterLeaveInfo.setAddress(rrsStoreBasicInfo.getProvince() + "," + rrsStoreBasicInfo.getCity() + "," + rrsStoreBasicInfo.getDistrict() + rrsStoreBasicInfo.getStoreAddr());
        enterLeaveInfo.setOutCode(applyCode);
        rrsGoodsEnterOrderDao.insertEnterLeaveInfo(enterLeaveInfo);
        //添加物料出库关系表
        transferGoodsInfos.forEach(transferGoodsInfo -> {
            // 判断单品管理物料
            if (ConstantPool.MaterielManagementType.SINGLE_MANAGEMENT.getCode().equals(transferGoodsInfo.getMaterielManagementType())) {
                for (int i = 0; i < transferGoodsInfo.getActualNum(); i++) {
                    RRSGoodsLeaveRelationEntity relationEntity = new RRSGoodsLeaveRelationEntity();
                    relationEntity.setGoodsId(transferGoodsInfo.getGoodsId());
                    relationEntity.setLeaveId(orderEntity.getId());
                    relationEntity.setGoodsType(transferGoodsInfo.getGoodsType());
                    relationEntity.setLeaveNum(IntegerEnum.ONE.getValue());
                    relationEntity.setExpectOutNum(IntegerEnum.ONE.getValue());
                    relationEntity.setStorePositionId(transferGoodsInfo.getOutPositionId());
                    relationEntity.setCreatTime(new Date());
                    relationEntity.setExNum(transferGoodsInfo.getGoodsTotal());
                    rrsGoodsLeaveRelationDao.insert(relationEntity);
                }
            } else {
                RRSGoodsLeaveRelationEntity relationEntity = new RRSGoodsLeaveRelationEntity();
                relationEntity.setGoodsId(transferGoodsInfo.getGoodsId());
                relationEntity.setLeaveId(orderEntity.getId());
                relationEntity.setGoodsType(transferGoodsInfo.getGoodsType());
                relationEntity.setLeaveNum(transferGoodsInfo.getActualNum());
                relationEntity.setExpectOutNum(transferGoodsInfo.getActualNum());
                relationEntity.setStorePositionId(transferGoodsInfo.getOutPositionId());
                relationEntity.setCreatTime(new Date());
                relationEntity.setExNum(transferGoodsInfo.getGoodsTotal());
                rrsGoodsLeaveRelationDao.insert(relationEntity);
            }
        });
    }

    @Override
    public LinkedList<Map<String, Object>> getAuditStatus(String applyCode, Integer auditStatus) {
        LinkedList<Map<String, Object>> status = new LinkedList<>();
        Map<String, Object> auditStatusOne = rrsGoodsTransferOrderDao.getAuditStatusOne(applyCode, 0);
        if(auditStatusOne!=null && auditStatusOne.size()>0){
            status.add(auditStatusOne);
        }
        if (auditStatus != 0) {
            Map<String, Object> auditStatusOne1 = rrsGoodsTransferOrderDao.getAuditStatusOne(applyCode, auditStatus);
            if(auditStatusOne1!=null && auditStatusOne1.size()>0){
                status.add(auditStatusOne1);
            }
        }
        return status;
    }

    public SysUserEntity getUser() {
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        return user;
    }
}
