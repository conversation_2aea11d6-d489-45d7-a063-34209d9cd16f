package bonc.storage.modules.storemanager.controller;

import bonc.storage.core.utils.PageUtils;
import bonc.storage.core.utils.Query;
import bonc.storage.core.utils.Result;
import bonc.storage.modules.storemanager.service.IRRSGoodsTransferOrderService;
import bonc.storage.modules.storemanager.vo.GoodsTransferOrderFromVO;
import bonc.storage.modules.sys.entity.SystemTokenEntity;
import bonc.storage.modules.sys.service.SystemTokenService;
import bonc.storage.modules.systemmanagement.dao.User2Dao;
import bonc.storage.modules.systemmanagement.entity.UserEntity;
import com.alibaba.fastjson.JSONObject;
import com.youngking.lenmoncore.common.utils.RedisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/RRSGoodsTransferOrder")
public class RRSGoodsTransferOrderControlller {
    @Autowired
    IRRSGoodsTransferOrderService irrsGoodsTransferOrderService;
    @Autowired
    SystemTokenService systemTokenService;
    @Autowired
    User2Dao userDao;
    @Autowired
    private RedisUtils redisUtils;

    /**
     * 调拨单列表
     * @param params
     * @return
     */
    @RequestMapping("/list")
    public Result queryGoodsTransferOrderList(@RequestBody Map params){
        PageUtils page = irrsGoodsTransferOrderService.RRSGoodsTransferOrderList(new Query(params));
        return Result.ok().put("page",page);
    }

    /**
     * 新增调拨单
     * @param goodsTransferOrderFromVO
     * @return
     */
    @RequestMapping("/add")
    public Result addGoodsTransferOrder(@RequestBody GoodsTransferOrderFromVO goodsTransferOrderFromVO){

        if (goodsTransferOrderFromVO.getTransferGoods().size()==0){
            Result.error(8888,"物料为空");
        }
        if (goodsTransferOrderFromVO.getStoreId()==null){
            Result.error(8888,"入库仓库为空");
        }
        if (goodsTransferOrderFromVO.getSrcStoreId()==null){
            Result.error(8888,"出库仓库为空");
        }
        String key = "storage_addTransferOrder_" + goodsTransferOrderFromVO.getApplyCode();
        boolean success = redisUtils.setIfAbsent(key, "addTransferOrder", RedisUtils.SECOND_EXPIRE_30);
        if (!success) {
            return Result.error("不能重复提交");
        }
        //将调拨的信息和物料信息保存
        irrsGoodsTransferOrderService.addTransferAndGoodsInfo(goodsTransferOrderFromVO);
        return Result.ok();
    }

    /**
     * 查询当前用户关联的仓库
     * @param token
     * @return
     */
    @RequestMapping("/queryStoreByUser")
    public Result queryStoreByUser(@RequestBody String token){
        SystemTokenEntity tokenEntity = systemTokenService.queryToken(token);
        //查询用户信息
        UserEntity user = userDao.queryUserById(Integer.valueOf(tokenEntity.getUserId()));
        List list=irrsGoodsTransferOrderService.queryStoreByUser(user.getId());
        return Result.ok().put("DATA",list);
    }


    /**
     * 根据仓库查询物料
     * @param params
     * @return
     */
    @RequestMapping("/queryGoodByStore")
    public Result queryGoodsByStore(@RequestBody JSONObject params){
        PageUtils page = irrsGoodsTransferOrderService.queryGoodsByStore(params);
        return Result.ok().put("page",page);
    }

    /**
     * 生成调拨单号
     * @param
     * @return
     */
    @RequestMapping("/queryApplyCode")
    public Result queryApplyCode(){
        String applyCode=irrsGoodsTransferOrderService.queryApplyCode();
        return Result.ok().put("applyCode",applyCode);
    }

    /**
     * 查看订单信息
     * @param
     * @return
     */
    @PostMapping("/queryTransfer")
    @ResponseBody
    public Result queryTransfer(@RequestBody JSONObject param){
        Map<String,Object> map = irrsGoodsTransferOrderService.queryTransferInfoById(param.getInteger("id"));
        return Result.ok(map);
    }

    /**
     *调拨审核
     */
    @PostMapping("/updateAudit")
    @ResponseBody
    public Result updateAudit(@RequestBody JSONObject param){

        return irrsGoodsTransferOrderService.updateAudit(param);
    }

    /**
     * 调拨审核详情
     *
     */
    @PostMapping("getAuditStatus")
    @ResponseBody
    public Result getAuditStatus(@RequestBody JSONObject param){
        LinkedList<Map<String, Object>> auditStatus = irrsGoodsTransferOrderService.getAuditStatus(param.getString("applyCode"), param.getInteger("auditStatus"));
        return Result.ok().put("status",auditStatus);
    }
}
