package bonc.storage.modules.storemanager.dao;

import bonc.storage.core.utils.Query;
import bonc.storage.modules.storemanager.dto.GoodsTransferOrderDTO;
import bonc.storage.modules.storemanager.entity.RRSGoodsLeaveOrderEntity;
import bonc.storage.modules.storemanager.entity.RRSGoodsLeaveRelationEntity;
import bonc.storage.modules.storemanager.entity.RRSGoodsTransferOrderEntity;
import bonc.storage.modules.storemanager.entity.RRSTransferGoodsInfoEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface RRSGoodsTransferOrderDao extends BaseMapper<RRSGoodsTransferOrderEntity> {
    List<Map> RRSGoodsOutOrderList(Query query);
    Integer RRSGoodsOutOrdertotalCount(Query query);
    List<Map> queryStoreByUser(Integer userId);
    List<Map> queryAllStore(Integer storeId);
    void addGoodsTransferOrder(GoodsTransferOrderDTO goodsTransferOrderDTO);
    void addTransferGoods(RRSTransferGoodsInfoEntity rrstransferGoodsInfoEntity);
    void updateStock(@Param("goodsId") Integer goodsId, @Param("goodsNum") Integer goodsNum,@Param("storeId") Integer storeId);
    void addGoodsLeaveOrder(RRSGoodsLeaveOrderEntity leaveOrderEntity);
    void addGoodsLeaveRelation(RRSGoodsLeaveRelationEntity goodsLeaveRelationEntity);
    String queryApplyCode(String param);
    String queryLeaveNum(String param);
    List<Map> queryTransfer(Query query);
    Integer queryTransferCount(Query query);

    List<Map<String,Object>> queryGoodsByStore(Query query);

    void insertAuditRecord(RRSGoodsTransferOrderEntity transferEntity);

    void insertAuditRecord2(@Param("applyCode")String applyCode,@Param("operateUser")String operateUser,
                            @Param("auditStatus")Integer auditStatus,@Param("auditUserId")String userId,@Param("noPassReason")String no);

    List<RRSTransferGoodsInfoEntity> queryTransferGoodsInfo(@Param("applyId")Integer applyId);

    void updateAudit(@Param("applyCode")String applyCode,@Param("auditStatus")Integer applyStatus);

    void updateActualNumById(@Param("actualNum")Integer actualNum, @Param("id")Integer id);

    void UpdateAuditRecord(@Param("applyCode")String applyCode,@Param("auditStatus")Integer auditStatus,@Param("auditUserId")String userId,@Param("noPassReason")String no);

    Map<String,Object> getAuditStatusOne(@Param("applyCode")String applyCode,@Param("auditStatus")Integer auditStatus);

    List<Map<String,Object>> getListById(@Param("id") Integer id);

    Integer queryGoodsByStoreCount(Query query);

    RRSGoodsTransferOrderEntity selectByOutCode(@Param("outCode") String outCode);
}
