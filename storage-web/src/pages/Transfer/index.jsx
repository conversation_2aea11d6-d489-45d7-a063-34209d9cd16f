import { useEffect, useState } from 'react'
import { View } from 'bonc-element'
import { connect } from 'dva'
import { Form, Select, Input, Button, Table, Modal, Message, Divider, Upload, Icon, DatePicker, Popconfirm } from 'antd'
import AccessoriesWin from './win'
import Service from './service'
import moment from 'moment'
import locale from 'antd/lib/date-picker/locale/zh_CN'
import 'moment/locale/zh-cn'

moment.locale('zh-cn')
const FormItem = Form.Item
const confirm = Modal.confirm;
const Option = Select.Option
const { MonthPicker, RangePicker } = DatePicker;
const AccessoriesPage = ({ form, list, totalCount, storeList, dispatch, visible, loading, history }) => {
  const { getFieldDecorator } = form
  const pageLimit = 10;
  const [searchParams, setSearchParams] = useState({ page: 1, limit: 10 });
  const [currentPage, setPage] = useState(1)
  const [title, setTitle] = useState('新建配件')
  const [currentId, setCurrentId] = useState(0);
  const [edith, setEdith] = useState(false)
  const [selectedRowKeys, setSelectedRowKeys] = useState({ ids: [] })

  //组件挂载
  useEffect(() => {
    getList({ page: currentPage, limit: pageLimit })
    getStoreList()
  }, [])

  const getList = (params) => {
    dispatch({
      type: 'transfer/list',
      payload: params
    })
  }

  const getStoreList = () => {
    dispatch({
      type: 'transfer/storeList',
      payload: {}
    })
  }
  const query = () => {
    let data = form.getFieldsValue()
    let time = data.time
    let beginTime = null
    let endTime = null
    if (time != undefined && time != null) {
      beginTime = moment(time[0]).format('YYYY-MM-DD HH:mm:ss')
      endTime = moment(time[1]).format('YYYY-MM-DD HH:mm:ss')
    }
    getList({...data, beginTime, endTime, page: currentPage, limit: pageLimit })
  }
  const addNewtransfer = () => {
    history.push({ pathname: '/storage/addTransfer' });
  }
  //跳转编辑掉包页面
  const editNewtransfer = () =>{
    history.push({ pathname: '/storage/editTransfer' });
  }
  //打开
  const handleWinToggle = (id) => {
    if (!visible && id > 0) {
      setCurrentId(id)
      setTitle('修改配件')
      setEdith(true)
      dispatch({
        type: "transfer/getInfoAc",
        id: id
      })
    } else if (visible && edith) {//关闭编辑
      setCurrentId(0)
      setEdith(false)
    }
    dispatch({
      type: "transfer/setVisible",
      visible: !visible
    })
  }
  const onChange = (page) => {
    setPage(page)
    setSearchParams({ ...searchParams, page });
    getList({ ...searchParams, page });
  }
  const handleAll = (idx) => {
    handleSchedule(selectedRowKeys.ids, idx)
  }
  //处理操作列
  const handleCheck = (listData,data)=>{
    let applyStatus = listData.applyStatus
    let queryINfo = listData.id
    let applyCode = listData.applyCode
    let NowtransferStatus = null
    if(listData.applyStatus == '待审核'){
      NowtransferStatus = 0
    }else if(listData.applyStatus == '审核通过'){
      NowtransferStatus = 1
    }else if(listData.applyStatus == '已拒绝'){
      NowtransferStatus = 2
    }
    switch(data) {
      case 'edit':
        history.push({ pathname: '/storage/editTransfer',state:{transferStatus:NowtransferStatus,applyCodeNew:applyCode,queryid:queryINfo,applyStatus:applyStatus}});
        break;
      case 'check':
        history.push({ pathname: '/storage/infoTransfer',state:{transferStatus:NowtransferStatus,applyCodeNew:applyCode,queryid:queryINfo,applyStatus:applyStatus,isWrite:true} });
        break;
      case 'info':
        history.push({ pathname: '/storage/infoTransfer',state:{transferStatus:NowtransferStatus,applyCodeNew:applyCode,queryid:queryINfo,applyStatus:applyStatus,isWrite:false} });
        break;
    }
  }
  const columns = [
    { dataIndex: 'fittingsNo', title: '序号' , render: (text, record, index) => {
        return ((currentPage-1) * pageLimit + index + 1)
      }
    },
    { dataIndex: 'applyCode', title: '单号'},
    { dataIndex: 'applyTime', title: '时间' },
    { dataIndex: 'storeNameOne', title: '出库方' },
    { dataIndex: 'storeNameTwo', title: '入库方' },
    { dataIndex: 'applyMode', title: '类型', render: () => {
      return '调拨'
      } },
    { dataIndex: 'actualNum', title: '数量' },
    { dataIndex: 'applyStatus', title: '审核状态'},
    {
      title: '操作',
      dataIndex: 'id',
      width: 150,
      render: (_, record) => {
        return <span>
        <span className="handle" onClick={() => { handleCheck(record,'info') }} style={{marginRight:10}}>查看</span>
        {record.applyStatus == '待审核'?<span className="handle" onClick={() => { handleCheck(record,'check') }} style={{marginRight:10}}>审核</span>:''}
        {record.applyStatus == '已拒绝'? <span className="handle" onClick={() => { handleCheck(record,'edit') }} style={{marginRight:10}}>修改</span>:''}
        </span>
      }
    },
  ]
  const rowSelection = {
    onChange: (selectedRowKeys) => {
      setSelectedRowKeys({ ids: selectedRowKeys });
    },
  };
  const config = {
    rules: [{ type: 'object', required: true, message: 'Please select time!' }],
  };
  return <View column>
    <View style={{ marginBottom: '20px', fontWeight: 'bold' }}>库存/掉拨</View>
    <Form layout='inline'>
      <FormItem label='单号'>
        {getFieldDecorator('applyCode')(
          <Input className="con-width" placeholder='请输入单号' />
        )}
      </FormItem>
      <FormItem label='出库仓库'>
        {getFieldDecorator('srcStoreId')(
          <Select style={{ width: '180px' }}>
            <Option value="">全部</Option>
            {
              storeList.map(item => {
                return (
                    <Option value={item.storeId} key={item.key}>{item.storeName}</Option>
                )
              })
            }
          </Select>
        )}
      </FormItem>
      <FormItem label='入库仓库'>
        {getFieldDecorator('storeId')(
          <Select style={{ width: '180px' }}>
            <Option value="">全部</Option>
            {
              storeList.map(item => {
                return (
                    <Option value={item.storeId} key={item.key}>{item.storeName}</Option>
                )
              })
            }
          </Select>
        )}
      </FormItem>
      <FormItem label='时间'>
        {getFieldDecorator('time')(<RangePicker locale={locale} />)}
      </FormItem>
      <FormItem style={{ marginLeft: '50px' }}>
        <Button icon='search' type='primary' ghost style={{ margin: '0 5px' }} onClick={query}>查询</Button>
        <Button type='primary' onClick={addNewtransfer}>新增调拨</Button>
        {/* <Button icon='upload' type='primary' ghost onClick={handleImport} >导入配件</Button>
        <Button icon='download' type='primary' ghost style={{ margin: '0 5px' }} disabled={selectedRowKeys.ids.length === 0 || loading} onClick={handleExport} >导出配件</Button>
        <Button icon='plus' type='primary' ghost onClick={handleWinToggle} >新建配件</Button>
        <Button icon='delete' type="primary" ghost style={{ margin: '0 5px' }} disabled={selectedRowKeys.ids.length === 0 || loading} onClick={handleAll.bind(this, 0)}>批量删除</Button> */}
      </FormItem>
    </Form>
    <View column style={{ marginTop: 10 }}>
      <Table columns={columns} dataSource={list} rowKey={row => row.key}
        pagination={{ current: currentPage, total: totalCount, onChange: onChange }} />
    </View>
    {visible ? (<AccessoriesWin title={title} close={handleWinToggle} edith={edith} />) : ''}
  </View>
}
export default connect(({ transfer, loading }) => ({
  list: transfer.list,
  storeList: transfer.storeList,
  totalCount: transfer.totalCount,
  visible: transfer.visible,
  loading: loading.effects['transfer/list']
}))(Form.create()(AccessoriesPage))
